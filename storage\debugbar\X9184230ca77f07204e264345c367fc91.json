{"__meta": {"id": "X9184230ca77f07204e264345c367fc91", "datetime": "2025-07-21 00:52:23", "utime": **********.336321, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753059142.7193, "end": **********.336352, "duration": 0.6170520782470703, "duration_str": "617ms", "measures": [{"label": "Booting", "start": 1753059142.7193, "relative_start": 0, "end": **********.217476, "relative_end": **********.217476, "duration": 0.49817585945129395, "duration_str": "498ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.217489, "relative_start": 0.49818897247314453, "end": **********.336355, "relative_end": 2.86102294921875e-06, "duration": 0.118865966796875, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46016808, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.028900000000000002, "accumulated_duration_str": "28.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266633, "duration": 0.02678, "duration_str": "26.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.664}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.306426, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.664, "width_percent": 2.284}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.316086, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 94.948, "width_percent": 2.561}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3253229, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.509, "width_percent": 2.491}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1038779455 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1038779455\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-769716892 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-769716892\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-159603636 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159603636\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2086133702 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753059091919%7C1%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZmbEtmZC83bDJpMmwwWWR6K2o4Q1E9PSIsInZhbHVlIjoibTI2YWpIUm4wZnZubWFhYTRqcmFadjJiWTdMdDhxdmx6Zzh0V2NwZG5SUEJqbUpOODR3cFlPMkVvVDhNOXNsdndYVFBZaktMUVoybzRDSlo4WlRzWUlqSTZObnh0QTRKVkJib1pMekpJUDhGNlB3UTFXTW5lS0h2ay9GZTc2Vmg4NnljQU54Tlo3KzJVRG15eWwyTHp3M0MvU3QyQXBhN0YvMGk2eUFwMDEvdGRwdlJ1UzZpM2lUWnk3ekp6MW9YQUg0UHZJMjBMc013N0FKMit6cHR3eVl3YkFubi84TENqUWRTVG9GVU5SVmZIeE5VaDMwZ0Y0SndlQUVqdEd6RERqVVJ5WENzVlZIeTFJWFdrUkl5TkxyV3pNSXpOSkNTN1lxbnF5VWs2cVJnU2l5NXR4bit5Z2VUWDE3WU41Z1E5aE1KY1Y4Y1h6dEFFaUd5YjNKUVdSeWlrY2tWRXFvd0p4RXRPam41OHY3T21SOXl5R2Rjem1rM1F6Y21oZWMzZVZxQlhlOGI3OE00c3pxUmMxZXVDaEpCUFdNSTUxd1Z1dDFkMTV4NUhNSkwrUlRwY0QwQkwwVVFEdmpneE5DMzhUY1BxY0tqS1RhNUx0YldwRmsrQkM2cTgraGxHMG9RNVNyN0c0YWhzUkEvU1NiZC94djlDaVNxaEFacGMyZlgiLCJtYWMiOiI4NzViMzM5MmM1YmY4YWRmMWNkM2I3MDk4NmRjOTMzMWE3N2MwMDA5Nzk3YjIzNjRiNDRjOTI1N2Y1OGNlNGUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklKM3ppTGVuemgzNFA2OTBRdjBlZWc9PSIsInZhbHVlIjoiT3lwTHRjY1pBZm9qNDNhaE1wa0prME5uU20zVHlQTjRPTWIvN1NLN0JVOVprOTlKNm1DejdnaEl4Rm96WGp2UTR6SFBkRXQ4OFVSY1ZVTGVleWF1Vkc1MW9STnF2TTgyVjdRajMraDk3VXZwbVAzbUtQTzBVcTN0SHVXQnNwK2JwV3Y2R0VGa2ZjdWsvUStadTk1UTBIbHZKUnBnUEJBcmpWZm4zKzNNUHNDdmtWdjZzRUdzQ1Jyd0pGaXJtei9xQ3Z0a0FTSGJMWFZCYnplZG1DZTlyTDluWG1FWXI3dHp2U3RlbEkwTlltdGdvc3ZtNGJMNXNHWU1mWXdjQlF1b1lHcG96UTRFQThqVlIzc0IwSzYrdlVxUnkvbXk3ZGJRbmo2YnNUemtBZWp2U0lOS09rZkxFTjlZTHVOMlR0Z1FsNVdJYi9WN05VYjZaMTJVNjVjREZXMDU0dEIwckRWSkxNbHpVdHVyWW9hcXorbWNzS2k0Z0dwSlBnN01NWkRxQmZORmwydlk1blhHSE45akNqWm03Z0lXaEwzNkZ3KzA1MWlIWFJSbC9XS3E1LzJzWEtSVDVlRmFSWnFaTDBKTnVpZERRY1UxQlVyWDhhZHFCbG5YZE1IYzdjdU1yTXI1dGtoVmhJdGVJMHp2MjR6bTdadlVIVldUN2U0aFh2bGQiLCJtYWMiOiI5ZDJlYTlhZTE0YjY0NTExOTc1ZmMzZDEzMzIyMjk2MjNhNGI4MTk1YWE4N2ExNjE1MDg3YzE2OTJhMzFiMGM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086133702\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-256678416 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Cu3UyOJpq8Q80cZEpugXvVrolpBjlhoa4viY2usT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256678416\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1731443267 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:52:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxoRGEwaDJORTA3MW9EVHVoblRySEE9PSIsInZhbHVlIjoiRE9VRlBHai9NdGk4dDFqQW5FUkZ1UTUzcjBMenBwbHJLeFBsU3VKYnlQYW5Cczc4ODRvd2FiakQ0bi8ydmh6ZjVMclZEL1ZVNjY4NlVwN1E2RDVOTW0xYU9nZHdROGRzUDN1VWM2NEhESUV2cEpGcW8zcElLMitCMVRXbFUrbCtySHJrdTVyRlZodkRMc0Zoc3lNZUxySmhMUlZXV3dpd252TzNQZ0lNMXMyWGVMSE9FekRUV0kybHE4ZGQxNTB1eHFoMkF2Q3o2eGVWMkdzaWU5QXU3di81RzNaZ0tlYllNNVI1WmsrNE4xcnlWMTl0L0Y2dFdEalUzMGhGQi80dG95cmYyaXJHc25IU2xBQTNzZzdtb0JWeUQ0Mk80YllIbllhTGNuMUNOcE00TWxCdGJ1cDgvaE8xeWM0K1BWMC9FWHhLSE9EUVpaMGJENFhEYlVJTmpPTWwvSFI1YVRCcERmK3hHaGNubzBUOU9EV004Y3M4U3UxOUZ2WXVyQ3EwazVIVzJMQk1aY1FTb1FRemRVVUZuRitHMXJJL2N2VDJFMnpPUmZVZU1tVXJpVXdVVzd0V1BaWCszL0JkR25sVFBxNEU0c2NjcTlEZ0dPNnNnYTJmYng5QmZvZDhNRXJEaVd4Z2JVdVljZnB2a3cwbm14b3ZaZTJlR0dvSm1NckUiLCJtYWMiOiI3OGQ5M2IzODRhNGRkMzZkODZjZDIwMzEwNThhNGUwMTFkMTk2MDcyZjdkYWFhNzA1ZTE0NDY3MGJkMDk4OTY1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:52:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImEzekVKdDhsU2xxY2RybWJzbEpqY2c9PSIsInZhbHVlIjoiRWhMYzNsWXlSeWcwbVB6OU96a0tlNll4OStqVyt1c2c0R3lwWTJBdXRvVFV0N0RQaGZ2R3VDSWlVdVlEdCtWeEEzVjFXNW1LRUVFcVcyU0k3RkNMZHhoVVh1dWFKT1hlZWJiQWhqZkZFY1VySW9zVlkwZUR5YnN2c2Nvd24xbFJTYlNwUGdraEdvaU1RZkZTTWlxZ01PMUowSmc3S05wbFRZY1dmS1BCc0FHWXc1dkxlK2RGNlJJR1NLbndPVExzVUlCU0ZqK1N0QzFiRHpZL1o0enpQeXBLN01BTkVWNUdlRDYzSjZDeHZUWDlJQWk4YUtqNzlMRXA0VFRteXp6dWpZUjNtaVNOVEs3Vnk4M2p5WXdMU0xjSFpXdXR6dVlXa1ByNy82OXhZd1NYenBvN2JtOElJT1dLdnRWVkwyQ1dNK21KbXE0SnNITjQ5UjNsYWQxQ2p1cG5qNzBsNzNYUkpxSHorRUpRMlF2dkZuRXU1WTM2QnlNSG9WUjlRbXBCdHFFWTNYWVhDMlYrakp2TVc0N1dXMG0zY3crR1NEVTJzRTlSZTRiWSsrWVVHNTJZNklxNTIyQ0JrNm5LYVFFVmttNmxoNHl1YnV3cFowNkg2NVRFOWxnNkpHckd0ZE9BcjRMb3RrNVZRRUhNSVM0RHVlZFdyb0s4Z1lwcEZIUjIiLCJtYWMiOiI5YTQyMjRhYjkwM2MzZWE3ZjJlODEyMDNkNGYxZjVlNmJkMzkwY2U0ZWJjOWFmZDEzZWZhNDZmNDQyMzhlNDQ4IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:52:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxoRGEwaDJORTA3MW9EVHVoblRySEE9PSIsInZhbHVlIjoiRE9VRlBHai9NdGk4dDFqQW5FUkZ1UTUzcjBMenBwbHJLeFBsU3VKYnlQYW5Cczc4ODRvd2FiakQ0bi8ydmh6ZjVMclZEL1ZVNjY4NlVwN1E2RDVOTW0xYU9nZHdROGRzUDN1VWM2NEhESUV2cEpGcW8zcElLMitCMVRXbFUrbCtySHJrdTVyRlZodkRMc0Zoc3lNZUxySmhMUlZXV3dpd252TzNQZ0lNMXMyWGVMSE9FekRUV0kybHE4ZGQxNTB1eHFoMkF2Q3o2eGVWMkdzaWU5QXU3di81RzNaZ0tlYllNNVI1WmsrNE4xcnlWMTl0L0Y2dFdEalUzMGhGQi80dG95cmYyaXJHc25IU2xBQTNzZzdtb0JWeUQ0Mk80YllIbllhTGNuMUNOcE00TWxCdGJ1cDgvaE8xeWM0K1BWMC9FWHhLSE9EUVpaMGJENFhEYlVJTmpPTWwvSFI1YVRCcERmK3hHaGNubzBUOU9EV004Y3M4U3UxOUZ2WXVyQ3EwazVIVzJMQk1aY1FTb1FRemRVVUZuRitHMXJJL2N2VDJFMnpPUmZVZU1tVXJpVXdVVzd0V1BaWCszL0JkR25sVFBxNEU0c2NjcTlEZ0dPNnNnYTJmYng5QmZvZDhNRXJEaVd4Z2JVdVljZnB2a3cwbm14b3ZaZTJlR0dvSm1NckUiLCJtYWMiOiI3OGQ5M2IzODRhNGRkMzZkODZjZDIwMzEwNThhNGUwMTFkMTk2MDcyZjdkYWFhNzA1ZTE0NDY3MGJkMDk4OTY1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:52:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImEzekVKdDhsU2xxY2RybWJzbEpqY2c9PSIsInZhbHVlIjoiRWhMYzNsWXlSeWcwbVB6OU96a0tlNll4OStqVyt1c2c0R3lwWTJBdXRvVFV0N0RQaGZ2R3VDSWlVdVlEdCtWeEEzVjFXNW1LRUVFcVcyU0k3RkNMZHhoVVh1dWFKT1hlZWJiQWhqZkZFY1VySW9zVlkwZUR5YnN2c2Nvd24xbFJTYlNwUGdraEdvaU1RZkZTTWlxZ01PMUowSmc3S05wbFRZY1dmS1BCc0FHWXc1dkxlK2RGNlJJR1NLbndPVExzVUlCU0ZqK1N0QzFiRHpZL1o0enpQeXBLN01BTkVWNUdlRDYzSjZDeHZUWDlJQWk4YUtqNzlMRXA0VFRteXp6dWpZUjNtaVNOVEs3Vnk4M2p5WXdMU0xjSFpXdXR6dVlXa1ByNy82OXhZd1NYenBvN2JtOElJT1dLdnRWVkwyQ1dNK21KbXE0SnNITjQ5UjNsYWQxQ2p1cG5qNzBsNzNYUkpxSHorRUpRMlF2dkZuRXU1WTM2QnlNSG9WUjlRbXBCdHFFWTNYWVhDMlYrakp2TVc0N1dXMG0zY3crR1NEVTJzRTlSZTRiWSsrWVVHNTJZNklxNTIyQ0JrNm5LYVFFVmttNmxoNHl1YnV3cFowNkg2NVRFOWxnNkpHckd0ZE9BcjRMb3RrNVZRRUhNSVM0RHVlZFdyb0s4Z1lwcEZIUjIiLCJtYWMiOiI5YTQyMjRhYjkwM2MzZWE3ZjJlODEyMDNkNGYxZjVlNmJkMzkwY2U0ZWJjOWFmZDEzZWZhNDZmNDQyMzhlNDQ4IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:52:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731443267\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-787837235 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787837235\", {\"maxDepth\":0})</script>\n"}}