{"__meta": {"id": "Xe89121fb0a90bc6c967bf1d016702161", "datetime": "2025-07-21 00:56:40", "utime": **********.747962, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.303676, "end": **********.747976, "duration": 0.44430017471313477, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.303676, "relative_start": 0, "end": **********.681133, "relative_end": **********.681133, "duration": 0.3774571418762207, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.681142, "relative_start": 0.37746620178222656, "end": **********.747977, "relative_end": 9.5367431640625e-07, "duration": 0.06683492660522461, "duration_str": "66.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46533312, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015500000000000002, "accumulated_duration_str": "15.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.70909, "duration": 0.01442, "duration_str": "14.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.032}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.732741, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.032, "width_percent": 2.903}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7386122, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.935, "width_percent": 4.065}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1549798086 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1549798086\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-127452767 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-127452767\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1249302592 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249302592\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2130471541 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753059391774%7C3%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRab2Z1WEYxakhVdkQ2dDhhTmtxdEE9PSIsInZhbHVlIjoieWEzSnVzYVIvZ21ZV3pXKzlNL0ZqOHpuQkc3Zkd6eUZHQmtRWGRNZ0VrVTRFbXRiWVpQYlVlcE9LUk9Pek9oNndEVXJRR2lJS0JKeDg2WHk4RUJxSit5ZnpnSDJ6RkRqK205N04rZUMzdHgzQ2ZhaWFzRUh1UWxVK1lpTHFUaVVaSUVHSkVnZUZ0NW5MeTc0YUdIazBIclRKbFlnV3Y4cFFYQkFoSHpYcUVRODhGNkk1dHFmM1diZkl6WS81eExzR1U2bHhzeFZCckIwaHhOZlNhVk9IQXc4c3pEZlhCUEdhMlhMRjRwdkFLT0hlak1YRlhERjJhOWNjdDAwcG52SEJnZmY0ZHdOQkRYYmxIdGtFaSs5S3BqREdsd3FVWGhxTitua2hjdjFseTZmVE44aS8rY0dDeUp4R2JUR05VWmVPUEsxQTBnUCs4bElBeWdDS2ZITEo0MjZvRnVrUXV0YWhaT0RETVN2c2czcENpTG1oeXVpWEtoOUpTbUNnUEJFOXFqMk8xNkJKNEZCNUhEZVlYYzBQdDhmZGpYQUhNa1FQakJaUlZTMnRnaitiU2ZEc0NOdGhoVWE4Y2tCelNHSVE5UEZaREZoWnozYWtpRjBMTlBtczB2ZllvZnBKc3ZQTEIzT3BaVVMrLzhIM29OVUs0OXArK083TTRkNXBXUE0iLCJtYWMiOiI3NDVjNWE5MTlkN2NjMTFkZjhmZTUzOTJiNWMxMjBhODUyOTZiYTJmODNiMGUzMTRlNWEwYWFiYmFlMzU0MDNjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZFYU1TRTVDTlN6bjYrajlVbkJsU2c9PSIsInZhbHVlIjoieDJrR0NpQU9tTHlmNnE2MFJjOGk3eVMxSlluVnZPWUNkdjJaQmt5T3duUFJubGw4OFB2bWFuMUFtVHNjWDNHTTQ5ZmRVQjRlOFVQWU5sTk9sNUg2dlBJUVpxUUx2Ty9RWm54T0VhZ3FqRE13MU9HTU9NZjF0WFNCUGJXbzMwYXZPcDZuY2ZDWXlLdXJHVWI2dXRMMklFaDh0bFdLTE4rQ3RWZjdBL3cydGZxN1BpM0RkYlJZOHE3YjhSRzdjMzhMUU9xeElUR2lKRFA3ZVA0OG9nMVhqbmJBWXRwbUNGdkpYcTBBQ0tnZUtoblBPY2lFT3QvUk42eEx2OFoxZ2tzbFBpVDVFL0k2cEdNeUd1dnV6RGtZOTFqTi8wblVNN3NKNTJCa2tvRzNwQ0l4TUFXTDAwK1pGQlYra1puWGpPbGROTGRLWDdNSWF1aU1vWElBaEpML3VkRWhXNzJJWmJYYjIyUnFKS0lzQ2RGbG80MEREMmJJbHBGZ3Q4eExjUGpQN3JabFBWcjBUZEQ1eEIyYzBkK1R4RE5WK21ROEw2UlI3YzRpVVBCbzRhc21POGFZTW81ZjlObHJCazR4ejVDd0o0dGJ6VGhHa09vQjgxbnF1aDNURW1iNlV0STlsejVzRjR2NE8rOEE0K3UxYnE0enJWZWcwTzFueDJoMTF4MFoiLCJtYWMiOiI1MWMzYmZjOWY5NDEzMGFkNjcwMTM4ZDA4MjUyNGJhODFhM2M1MWZmYTJjNjJiZDkyYTE1OWRlZDIxOTI1NzA5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130471541\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2143526593 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2143526593\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1492202637 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:56:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhaM1AyOHBFOURsckkrRWJkTXl4RlE9PSIsInZhbHVlIjoiZjgydHlnUk1ZMHNPeXc0M1g1SjB2eXJzZFZ4bU9zU202TW9ybmlVSEczMmt4aTlTRlpDVEFKZ3BReVJOakI1VmYyVnRQNkpURzQ2L3JLaDEydXdzakVoYXlwZVR1TWtNWjUxUmJISGZ3R2t4ZDQzcGN6cWx5T2lMUC9BTkpLUDFpWFU5cGJOSHM1TlcreGFFR1l4U2R3Y3lwbWVXbHVTTHFXRm1hTEMwZkwzQmFnRmpOSFhpVmhRMlNTKzRWVzE5bnRRK3RhZFVBalRSWHZPRDRqL05kcHhHTWV4U3RZLzZZMnlIRVVwMEtUa05nVFNYd2QrNGFXQzArTm5KOW11NUtiNjd4NEdVMmpzQjRhbEFWNXNJSGJkOHZVS0tQZVIzUE84aTBFajRGZGhxTWJKMnE0eHc3Y2pQd3gvWlZSMEtOYWVpNG5mY1JCb3V1TmhDN0dCdTMyNExyNUxQc0pMZHFScDB4ZFBNN2JZeml1aHhsOTdWN0RoTDMxdDdycUxYdDdoaDN1akgrRFFiaGQ1a0RmR2lhcHVQSVRuMURPUkJrbjI4dTNTUUF2dEJSOWVyQThhZHIyL1BmS0xFd0hFZEJ5M09KNEtraEdrT2tza3hBZDBmTlF1MXcwVmE5OEZhTGlsYVJLemU3aC9sSHZLN1ByNDJDWE03MVJ4LzA4WEQiLCJtYWMiOiI1ZDY3Y2MxYzc4MDAwNzQ0MWJlNzM0ODg4MWU2NzdkYTNlMTdiYzQyNTIyODJlODA3M2U1MTkxMDEyOWZjNDAwIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im42WmNMUmV4S2MzNUdqbko3a21VWkE9PSIsInZhbHVlIjoicm9maWZ4RTJBZEFlZWFlWUlZcWFtK3l3UDNZQ21zRkY2c3MzQnFmWEJWWXNSb0IzNWZDNUNxY2VoOWlkb1dDWUIyRm5PWmhqVEFwTENYVURhcXFqTzRsbkJhRVBZTmhpQjg2RWlic3JZQ3VJTU1aaEZWS2VRM0xpc1JEUlY0WER0NFJiQ3hDeGhGT25YTnpudzJ0Z2wwTkhHSkNDRnJHTkpFYXd2VTBnd2xDYnNOZ2FKdEk2Mjh6MjhjZHd1WWlZaWgxS0RLT3hkMGRndlNjdmdIRjgvdXNEYTduNjRxaHFGTXpZYkFTc2diaTl0bEhIK1BKOHZzRExueWxwb2pFTzNLR1NvSDY2ZzRUaEVjc3l5cFNkMUdvYktlK1JGMnRDZmU2RVYxLzd3NU01cEZvMlJGNlExU25XVGxPMkN0dmJaM0s2Q3hDRDNGbG1nYXQxalZhM0lnakJoU2VEZVZpTWl3Yi8xVzdJL0xEcWdhV3JpQ041Z3hTVXNqdUluVS9lb01hdWgvcGlxYVNsdm9FbGxVaUlaaDdPaXN6MjJDL0t0enhqdVZSeXlxcnVHYjNFVUFiZ09RUEIybEZUYnp4aFJ6OGZsK3FTZHhmM0tLS2gwQlVSb0FXeDdldWk4OXRhdDRvZnhiUnZid1JOUXJoQTViRWRyMGhyT0tkV2p1YTAiLCJtYWMiOiJlMDVmMDU4Yzg4YzRjNTQyMjdkMzVlMDZmOGNmNDY5MjBiMmVlODExYWM1N2YwYmI2OTY3Y2NlMmNkOTkyMTFiIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhaM1AyOHBFOURsckkrRWJkTXl4RlE9PSIsInZhbHVlIjoiZjgydHlnUk1ZMHNPeXc0M1g1SjB2eXJzZFZ4bU9zU202TW9ybmlVSEczMmt4aTlTRlpDVEFKZ3BReVJOakI1VmYyVnRQNkpURzQ2L3JLaDEydXdzakVoYXlwZVR1TWtNWjUxUmJISGZ3R2t4ZDQzcGN6cWx5T2lMUC9BTkpLUDFpWFU5cGJOSHM1TlcreGFFR1l4U2R3Y3lwbWVXbHVTTHFXRm1hTEMwZkwzQmFnRmpOSFhpVmhRMlNTKzRWVzE5bnRRK3RhZFVBalRSWHZPRDRqL05kcHhHTWV4U3RZLzZZMnlIRVVwMEtUa05nVFNYd2QrNGFXQzArTm5KOW11NUtiNjd4NEdVMmpzQjRhbEFWNXNJSGJkOHZVS0tQZVIzUE84aTBFajRGZGhxTWJKMnE0eHc3Y2pQd3gvWlZSMEtOYWVpNG5mY1JCb3V1TmhDN0dCdTMyNExyNUxQc0pMZHFScDB4ZFBNN2JZeml1aHhsOTdWN0RoTDMxdDdycUxYdDdoaDN1akgrRFFiaGQ1a0RmR2lhcHVQSVRuMURPUkJrbjI4dTNTUUF2dEJSOWVyQThhZHIyL1BmS0xFd0hFZEJ5M09KNEtraEdrT2tza3hBZDBmTlF1MXcwVmE5OEZhTGlsYVJLemU3aC9sSHZLN1ByNDJDWE03MVJ4LzA4WEQiLCJtYWMiOiI1ZDY3Y2MxYzc4MDAwNzQ0MWJlNzM0ODg4MWU2NzdkYTNlMTdiYzQyNTIyODJlODA3M2U1MTkxMDEyOWZjNDAwIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im42WmNMUmV4S2MzNUdqbko3a21VWkE9PSIsInZhbHVlIjoicm9maWZ4RTJBZEFlZWFlWUlZcWFtK3l3UDNZQ21zRkY2c3MzQnFmWEJWWXNSb0IzNWZDNUNxY2VoOWlkb1dDWUIyRm5PWmhqVEFwTENYVURhcXFqTzRsbkJhRVBZTmhpQjg2RWlic3JZQ3VJTU1aaEZWS2VRM0xpc1JEUlY0WER0NFJiQ3hDeGhGT25YTnpudzJ0Z2wwTkhHSkNDRnJHTkpFYXd2VTBnd2xDYnNOZ2FKdEk2Mjh6MjhjZHd1WWlZaWgxS0RLT3hkMGRndlNjdmdIRjgvdXNEYTduNjRxaHFGTXpZYkFTc2diaTl0bEhIK1BKOHZzRExueWxwb2pFTzNLR1NvSDY2ZzRUaEVjc3l5cFNkMUdvYktlK1JGMnRDZmU2RVYxLzd3NU01cEZvMlJGNlExU25XVGxPMkN0dmJaM0s2Q3hDRDNGbG1nYXQxalZhM0lnakJoU2VEZVZpTWl3Yi8xVzdJL0xEcWdhV3JpQ041Z3hTVXNqdUluVS9lb01hdWgvcGlxYVNsdm9FbGxVaUlaaDdPaXN6MjJDL0t0enhqdVZSeXlxcnVHYjNFVUFiZ09RUEIybEZUYnp4aFJ6OGZsK3FTZHhmM0tLS2gwQlVSb0FXeDdldWk4OXRhdDRvZnhiUnZid1JOUXJoQTViRWRyMGhyT0tkV2p1YTAiLCJtYWMiOiJlMDVmMDU4Yzg4YzRjNTQyMjdkMzVlMDZmOGNmNDY5MjBiMmVlODExYWM1N2YwYmI2OTY3Y2NlMmNkOTkyMTFiIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492202637\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2047380444 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047380444\", {\"maxDepth\":0})</script>\n"}}