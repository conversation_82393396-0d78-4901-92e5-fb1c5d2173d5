{"__meta": {"id": "Xd27d68d6731db2ccb81e19db9bf7ad17", "datetime": "2025-07-21 00:56:31", "utime": **********.908486, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.465242, "end": **********.908507, "duration": 0.4432651996612549, "duration_str": "443ms", "measures": [{"label": "Booting", "start": **********.465242, "relative_start": 0, "end": **********.842447, "relative_end": **********.842447, "duration": 0.37720513343811035, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.842458, "relative_start": 0.377216100692749, "end": **********.908508, "relative_end": 9.5367431640625e-07, "duration": 0.06605005264282227, "duration_str": "66.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46677584, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00311, "accumulated_duration_str": "3.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.87235, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.846}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.882967, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.846, "width_percent": 16.399}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.88917, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.244, "width_percent": 15.756}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1478045549 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1478045549\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1202015451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1202015451\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-820777362 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820777362\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1020163950 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753059387485%7C2%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVXa3BzUXpZSXVySWtCNkMrK3g4RUE9PSIsInZhbHVlIjoiN2J2aTY3YUR5TTAwcGVyOTNIR3ByM1B0TW9XeW9WU0pRcHFsWVZKOFUyWFlqR0h0cHpRT3lmSkNZV0dlOU4vdUpqTWVKWER2ellQNWdVNU03elpaRkVCRDByT2tGcG5kb3NOTmRSd0RyT2tpZ05zNGZLYnU1SDNSakprck1zU1dyd1NtMHpScjMvTTFkT3FUOTM4WGJqdmFYZnd4SGlqcDNKMGU3MExkRGhBOG9IYlNJQjE0b3pYV1RXWllvNjUyd2pSVllxQVQwUWlRcWhnUUQ3OGY4MDdmSlBlODVmM3FrcWEwcUp3cnlEZ1hidTlyRk9BN1ZseExqVVBWMWVQTGQ5VFZ5d1JnTVZqS1J1RnB3NVZzOEFoL1hZNjN6RHlpVytTQWVXYjZPdndQam5QOEJjakdBS1FrY1FzVzc4UXBOS3ZLUnlWTHRDUnVXanZBVTU4elRzMjVGYVIrMktIK3g0dVFDRFA3dVV2Sm9CQk1yQzU0ZGw4YUZPdUd4NTZPZzVzVWhqcFQ1cW9pNGdtS013TlZObXM5U0w1dmRjVGZRNUt1bm9ESlh0WThFQ2dXZnRNeUxWdkJ3ZWxDcFRoSE5WK1M0WjNOUUc3R3d1SUhJUU1KVFVocDU2SDAvbWxSc1c0amc3RERmSzhBemsvbllOVmRPSjk5cGxDME1VZlEiLCJtYWMiOiI3NmM1MTMyNTNkZGRhYmY2ZTA0MmY2ZTczYTVlZTQ5MzhiMGI3YThhMTZhOWExYzIwZTZlMTM0MGViOGUyN2EwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1ZYjFlbjV4TjRWUG5NdmVSbHdteVE9PSIsInZhbHVlIjoiZHRjRFhldWVCUnAzRmdnMGdmR3VzbjNMNGxRWXExdlNjSVFPQ1JENWhmSUZpMTNxYXVDOXVHZkhvcDg2RUk3Mk5uQjUxZWIxcHpvbHJBYW96d0tZY0NXR3E3MFhldWI0NGprTUhWcVNRSzE1QXF0V2dBVm9pa29tVXJkcmczYWRVdzYvajZySFkvMDVCVGc3cHMvQjE1T05CRnU1ZlFUQ3RWSVU5ZGdpTjhRNTdYcStmejhvU1BaM2xGT2VZM0NTMTl4VjZnVkEwZkYzaUtZMDFMdWlWVXIwcms3QlZYcC84UnRjRGF5ZElZKzJPUDhZeldseXFKdDJ6WlhYY28zV1VHamhRd09TbGZHbUVCR2d1MjVoRXRmVVNxK1krcDdIUWd0azFvV3JsODdyYVdGdGRsU1dQcmU2SWg2V0IzWC84TldPME12OEpxZTdkbGRORGpLd2pkWGQvOHR5eTJkeE1FTnZzdHIxNlk4TlVVZEtGaWJud3ArOXUrd0J3Mk9yZ21UMWluQW5vT0NtUlpyZmltbS9xSGx5NTQrNVJrbVBrZE41b2ZYYUsvS1UrOHN1S3RUdzRZTFdzUk1tbVRLQlVZNUpJRWo0Ujg0NlFoejYya2xkV1hvbkVUbUhBNVBFRkdoYmhseXBscGt6Nm5pR3VIOFBBSk5ZSU9peWx4Q2QiLCJtYWMiOiJhNThjZTFmZTM2YmFlMGM2NGFmYzBmOGRiZmNlYTE1NGI5MTIwOTg3YmYwMWJhMDQ0Y2FjOGMyNGVjYzE3NGJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020163950\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-947905742 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947905742\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1155516606 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:56:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJYbEpsNEJwbkc2V3hyNWhqRVQ4cHc9PSIsInZhbHVlIjoicUJhSzJ3WlI2U245R3RLcldvbitnVmdxQUNQblJwdmxRTkp6eVhDeGRVOHcwL1VtR3hvOHpwZTdLOGFOeG9Zb1VsNXc3VjJLMTgxUVYxQlk0NEFyN04ycG04bmVDU1JkTEpNODUxTlgxUUVUK2FyL2NqQ3BzalRXWnAyYUZMSDFFbnk2bHNEbzdOWXJBcldmdmxIK3Z5ZEVEZDJjYlVMYjc5dFNDbTAzUGdDaWJhdUpzdkYwRDdNaVZ0ZjNINjU0R1hKTkdYWVFVNnNBMVBvUExUNm1tMDMvd2ZFVmFydkY3b2Ewdk8yaCttKzBnd1UyVnFWMVJyc1ZBUjdsRFRFRUw1MzJ6YTFWOCtyWU5ST0N0TFlUNWhSTXVxUUE1VDlyV2wyaVMwbUxCTEZFaWxoOVl1RDBRZ0RBbEtJOXI2YmxIR2Y0U1Y3NTV3ZWFVWE8wc1pkZE90Y1o0UVZiS1JhUUxRaHJkWWN4NzVYYmlrQ295bzZVcXFmWnowd3dtNExUVVFDZlg5cmJGc1JiNWxIWDNKM29lSjJVaTV6VHJFYy9JM2VXQVltbGFDbSt4N1QwbDdtYldQNlFTNktuVmFNNDBrVUlCMFd3UnVwMkJHUGduRkRlVzNMcVQza2MveVN2MUcrOWUvMUprS3dnTzVTOG11T21haEdpR09XakpyY1UiLCJtYWMiOiI1YTkyOWM3NjdhNDhlMDdmOGI3YWQwOWE2MWU4M2JkMWZjY2ExZTRlNmVkMWJkODcwNTlkY2Q5YzdlZDMyMTczIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitCQk5ZOGVJUVlOc1BRbS84eGhMd3c9PSIsInZhbHVlIjoibGhBMUFhYkFnYVZDMVZ3UkJ6VW5LQVJZRmlXc293Q0xydmpIWDdwRGRRSWdKcWduQjhBUmx4T3RUOGErT0VHUTNINlRVWnhGWHZ6d0VkaEp3TDlMRW92OFJMZVVrS0t5Q0ozcThkR29hRnNIQS9EVFpITnRhQ0d0Qm9Cb2lRQlpzM0cxQUNJMzlJblZnZmM1MkM2cXJSN29lM1Z0QWIyRElMV240eis5YlYrSHAzK0t1MUdjTFBlUnhIYzZ0QnlseDBOemhUbDd4WWZYUmhTZ2VFY1JGZkh2YjRkRm5qS1h2cnRicml0bWtUR3B0aUdSdHd0UG02cEduekN0dU9rQzEyQjBBSzViTndpdXpZMDB6a2xDMjdaZkVuOHR4VUFTTG1EY0hrWFlnZ0hqYzdXVno4aFVDNGVVQlY2cStGMXJiOWl6ZjQwb2kzQnp5TWlEOE9nazR3Ym1vd094YWVieW5qSjJTWnU5U2xZRkRSbnhia1ZmaHR4bWJHV2tGaHE0TFBDNVVWZUVNTm1IQVlYV0hmY0x0enhZL01wMkVyYlFBcUpSYTVCN3dYeU5qcmpNbGI4V2trL0dMTmM0cWx1Y1FyWVIzUWVvRy9HNGhJUUs5SWl6STdoc2pEU1h1ekJkQ2RYa1ByQm1WcldYUjllL05vU1hsNlhDVzhQcEpBNXAiLCJtYWMiOiI2N2I0ZmU5MmU3MjFkNDgxNmJjOGNlZTY5YTIxOGI0OTQ3NjcxZjJmY2Q3NWQwYjZhMDc2ZGMzNWFlMWM1NmY5IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJYbEpsNEJwbkc2V3hyNWhqRVQ4cHc9PSIsInZhbHVlIjoicUJhSzJ3WlI2U245R3RLcldvbitnVmdxQUNQblJwdmxRTkp6eVhDeGRVOHcwL1VtR3hvOHpwZTdLOGFOeG9Zb1VsNXc3VjJLMTgxUVYxQlk0NEFyN04ycG04bmVDU1JkTEpNODUxTlgxUUVUK2FyL2NqQ3BzalRXWnAyYUZMSDFFbnk2bHNEbzdOWXJBcldmdmxIK3Z5ZEVEZDJjYlVMYjc5dFNDbTAzUGdDaWJhdUpzdkYwRDdNaVZ0ZjNINjU0R1hKTkdYWVFVNnNBMVBvUExUNm1tMDMvd2ZFVmFydkY3b2Ewdk8yaCttKzBnd1UyVnFWMVJyc1ZBUjdsRFRFRUw1MzJ6YTFWOCtyWU5ST0N0TFlUNWhSTXVxUUE1VDlyV2wyaVMwbUxCTEZFaWxoOVl1RDBRZ0RBbEtJOXI2YmxIR2Y0U1Y3NTV3ZWFVWE8wc1pkZE90Y1o0UVZiS1JhUUxRaHJkWWN4NzVYYmlrQ295bzZVcXFmWnowd3dtNExUVVFDZlg5cmJGc1JiNWxIWDNKM29lSjJVaTV6VHJFYy9JM2VXQVltbGFDbSt4N1QwbDdtYldQNlFTNktuVmFNNDBrVUlCMFd3UnVwMkJHUGduRkRlVzNMcVQza2MveVN2MUcrOWUvMUprS3dnTzVTOG11T21haEdpR09XakpyY1UiLCJtYWMiOiI1YTkyOWM3NjdhNDhlMDdmOGI3YWQwOWE2MWU4M2JkMWZjY2ExZTRlNmVkMWJkODcwNTlkY2Q5YzdlZDMyMTczIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitCQk5ZOGVJUVlOc1BRbS84eGhMd3c9PSIsInZhbHVlIjoibGhBMUFhYkFnYVZDMVZ3UkJ6VW5LQVJZRmlXc293Q0xydmpIWDdwRGRRSWdKcWduQjhBUmx4T3RUOGErT0VHUTNINlRVWnhGWHZ6d0VkaEp3TDlMRW92OFJMZVVrS0t5Q0ozcThkR29hRnNIQS9EVFpITnRhQ0d0Qm9Cb2lRQlpzM0cxQUNJMzlJblZnZmM1MkM2cXJSN29lM1Z0QWIyRElMV240eis5YlYrSHAzK0t1MUdjTFBlUnhIYzZ0QnlseDBOemhUbDd4WWZYUmhTZ2VFY1JGZkh2YjRkRm5qS1h2cnRicml0bWtUR3B0aUdSdHd0UG02cEduekN0dU9rQzEyQjBBSzViTndpdXpZMDB6a2xDMjdaZkVuOHR4VUFTTG1EY0hrWFlnZ0hqYzdXVno4aFVDNGVVQlY2cStGMXJiOWl6ZjQwb2kzQnp5TWlEOE9nazR3Ym1vd094YWVieW5qSjJTWnU5U2xZRkRSbnhia1ZmaHR4bWJHV2tGaHE0TFBDNVVWZUVNTm1IQVlYV0hmY0x0enhZL01wMkVyYlFBcUpSYTVCN3dYeU5qcmpNbGI4V2trL0dMTmM0cWx1Y1FyWVIzUWVvRy9HNGhJUUs5SWl6STdoc2pEU1h1ekJkQ2RYa1ByQm1WcldYUjllL05vU1hsNlhDVzhQcEpBNXAiLCJtYWMiOiI2N2I0ZmU5MmU3MjFkNDgxNmJjOGNlZTY5YTIxOGI0OTQ3NjcxZjJmY2Q3NWQwYjZhMDc2ZGMzNWFlMWM1NmY5IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155516606\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-476154122 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476154122\", {\"maxDepth\":0})</script>\n"}}