{"__meta": {"id": "X5805953262f4bd0e58936dd12b81532c", "datetime": "2025-07-21 00:53:36", "utime": **********.08543, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753059215.483236, "end": **********.085452, "duration": 0.6022160053253174, "duration_str": "602ms", "measures": [{"label": "Booting", "start": 1753059215.483236, "relative_start": 0, "end": **********.034906, "relative_end": **********.034906, "duration": 0.5516698360443115, "duration_str": "552ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.034916, "relative_start": 0.5516798496246338, "end": **********.085454, "relative_end": 1.9073486328125e-06, "duration": 0.050538063049316406, "duration_str": "50.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44310944, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0020299999999999997, "accumulated_duration_str": "2.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.069204, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zqABoP3S0OH0iYPBwMdcxBpGaKLxBXlts7Sh19WX", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1259618903 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1259618903\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1245432750 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1245432750\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-922528827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-922528827\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-249694647 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"137 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-249694647\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1141811915 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141811915\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2138970679 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:53:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVSN2xDM2ZiSXVzbXRSZ3RqTklXclE9PSIsInZhbHVlIjoiQVAwOVNsNkIySC9GdzhaZk5mSXVIWkFERmZLK09LR09vTWdOamRuOWo3Qnp5aW1uOFlJWGhHQ1J5WlRJdjVobTQ5cnNHWHNzSWd3eWRpQmdUMU9icVpjdmxjUWFLUGRNZmJYVC9TRTExQmVsVitoMUVveHZxR2F0TWNlcE1sVlllTkJZa3A0eUtSZGFxd0psQjV1UUcvQmd3YzRRTXdHYlVWSW9pZHVSblZlaEZwaWhjNFB5Q0F1S2ZCSXN3eXhDOWc0QUdPVkpvQkJ0YTcyZUM3anpZV1dlM0xnczhyUFFtY3B1WDRleFVHdTFOQ2ZvMG9lN3BmVW1lN1JiUE1yaWszZXRxemJvL2czbUtwZXdxeUtnTmgvUks5blo3d0VZU0xzV0cvclVJSk1EdDVxdWU4Yko1cnFHY3pEcjJyY2JTOEVXZVZzL0YxYTRsWXZLVXJHVzJWSStzOGZ6V1RsVmFOcUdTc0tNS0I0NGtaSkRRK2RLN3pZeHdXWnBHRmxVUmNIbkx3Q2hNSVlSY2QzbkRQWXZ2dlJTNTljZUZZb1hmSUF0WnZ4cnI4SUFZM2p2T21ya3Z1NXg5aXk3QTlyUW51WXl1N3kzVmVuenJRQmpnTWpVOThTWTlBTHVTaXk5WFVMNzRUaUh3MnE1dkpuZkRBcitnaVkwQ1lRZVdmenciLCJtYWMiOiI0ZWE5N2Q4NDYyNDEyMDFkYWY3OTc4NWU3Y2I3MmQyMDVjZDM1MjFjOTM0ZTI2NzJlNzZiNzY4ODU3NGIxNThhIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:53:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFqcCs5d3kyOW1GUXhlSWNnRVFMZXc9PSIsInZhbHVlIjoicjY3V1lUSDA5WVUxcmZzeW8vOEV1R2M4WEtFYmdsNXdqZFZDSFBVYkp4am83YTVhRjY5c0hhL3BWUCtqSUZXVjB3ZmpZaUtraGxtZlk5dlVjRmtPVTZyOXZzR1JHazZyL0puM3lKZ0lJcnYzUVp0UEJRdWY4S1p6VWVBdkt0dkhzTWxKdXFzQ2dHaWhFT0VOT1hIbTBLTEFHZ2dIeG1LcDVubjNBV2NYT2dxNFpqczYrS1FWb0FGdXlUdVBneFpsS1hqQWRUTzZVM3dPd2huaXBIbHhlcDlKOWpraWpub2lxVUx3ckJiSkh2TDFyNUZEeGhsYngycjJDVzdQK3lEelZYUTZiVEtOUDVsK2R6VW9ZdWNOT0lXL1hSaS9BLzNMaHVDTGlMVFFmM3N6aSt6RUtiYnRjVDZjQUs5SWxrVW4rS3A0RmVCSERrY0ZxTmFPUk5jVmRwb0twWVh4TDVLWnE0QzFRUTF4cll1VzlBaSt3cTBxUjJUOU0rQVA4Nk9zMnZiMmNZVWFaNDIrTUE0TTVwb2FTY05SMXFyTWxCM3ZaK0xOSStlemRhaHJicWNXSGc2WEJJTVk0b0cvUG9McjRzWXFjZHkwakxuT0Q3UDM1VG52TUR0eHZ0YnJqS2dOTU56QmJHUmR3S29xd2EwZjJ0SzRsZ2tLVEROc0RIWm0iLCJtYWMiOiI5YjFjNjU5ZWVjNWI5OTNkMmZjNmYwMWMxOTM0OWUyOWFiNDUzZWQ1NmQzMTYxMzk1ZmVlOTgxZmEyMGY5ZjhmIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:53:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVSN2xDM2ZiSXVzbXRSZ3RqTklXclE9PSIsInZhbHVlIjoiQVAwOVNsNkIySC9GdzhaZk5mSXVIWkFERmZLK09LR09vTWdOamRuOWo3Qnp5aW1uOFlJWGhHQ1J5WlRJdjVobTQ5cnNHWHNzSWd3eWRpQmdUMU9icVpjdmxjUWFLUGRNZmJYVC9TRTExQmVsVitoMUVveHZxR2F0TWNlcE1sVlllTkJZa3A0eUtSZGFxd0psQjV1UUcvQmd3YzRRTXdHYlVWSW9pZHVSblZlaEZwaWhjNFB5Q0F1S2ZCSXN3eXhDOWc0QUdPVkpvQkJ0YTcyZUM3anpZV1dlM0xnczhyUFFtY3B1WDRleFVHdTFOQ2ZvMG9lN3BmVW1lN1JiUE1yaWszZXRxemJvL2czbUtwZXdxeUtnTmgvUks5blo3d0VZU0xzV0cvclVJSk1EdDVxdWU4Yko1cnFHY3pEcjJyY2JTOEVXZVZzL0YxYTRsWXZLVXJHVzJWSStzOGZ6V1RsVmFOcUdTc0tNS0I0NGtaSkRRK2RLN3pZeHdXWnBHRmxVUmNIbkx3Q2hNSVlSY2QzbkRQWXZ2dlJTNTljZUZZb1hmSUF0WnZ4cnI4SUFZM2p2T21ya3Z1NXg5aXk3QTlyUW51WXl1N3kzVmVuenJRQmpnTWpVOThTWTlBTHVTaXk5WFVMNzRUaUh3MnE1dkpuZkRBcitnaVkwQ1lRZVdmenciLCJtYWMiOiI0ZWE5N2Q4NDYyNDEyMDFkYWY3OTc4NWU3Y2I3MmQyMDVjZDM1MjFjOTM0ZTI2NzJlNzZiNzY4ODU3NGIxNThhIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:53:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFqcCs5d3kyOW1GUXhlSWNnRVFMZXc9PSIsInZhbHVlIjoicjY3V1lUSDA5WVUxcmZzeW8vOEV1R2M4WEtFYmdsNXdqZFZDSFBVYkp4am83YTVhRjY5c0hhL3BWUCtqSUZXVjB3ZmpZaUtraGxtZlk5dlVjRmtPVTZyOXZzR1JHazZyL0puM3lKZ0lJcnYzUVp0UEJRdWY4S1p6VWVBdkt0dkhzTWxKdXFzQ2dHaWhFT0VOT1hIbTBLTEFHZ2dIeG1LcDVubjNBV2NYT2dxNFpqczYrS1FWb0FGdXlUdVBneFpsS1hqQWRUTzZVM3dPd2huaXBIbHhlcDlKOWpraWpub2lxVUx3ckJiSkh2TDFyNUZEeGhsYngycjJDVzdQK3lEelZYUTZiVEtOUDVsK2R6VW9ZdWNOT0lXL1hSaS9BLzNMaHVDTGlMVFFmM3N6aSt6RUtiYnRjVDZjQUs5SWxrVW4rS3A0RmVCSERrY0ZxTmFPUk5jVmRwb0twWVh4TDVLWnE0QzFRUTF4cll1VzlBaSt3cTBxUjJUOU0rQVA4Nk9zMnZiMmNZVWFaNDIrTUE0TTVwb2FTY05SMXFyTWxCM3ZaK0xOSStlemRhaHJicWNXSGc2WEJJTVk0b0cvUG9McjRzWXFjZHkwakxuT0Q3UDM1VG52TUR0eHZ0YnJqS2dOTU56QmJHUmR3S29xd2EwZjJ0SzRsZ2tLVEROc0RIWm0iLCJtYWMiOiI5YjFjNjU5ZWVjNWI5OTNkMmZjNmYwMWMxOTM0OWUyOWFiNDUzZWQ1NmQzMTYxMzk1ZmVlOTgxZmEyMGY5ZjhmIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:53:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138970679\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2109307449 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zqABoP3S0OH0iYPBwMdcxBpGaKLxBXlts7Sh19WX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109307449\", {\"maxDepth\":0})</script>\n"}}