{"__meta": {"id": "X8d580134ffde3b23b181adb0dbb5a61c", "datetime": "2025-07-21 00:52:24", "utime": **********.342735, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.870008, "end": **********.342756, "duration": 0.****************, "duration_str": "473ms", "measures": [{"label": "Booting", "start": **********.870008, "relative_start": 0, "end": **********.272444, "relative_end": **********.272444, "duration": 0.****************, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.272454, "relative_start": 0.*****************, "end": **********.342759, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "70.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036, "accumulated_duration_str": "3.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.308914, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.611}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.32355, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.611, "width_percent": 18.056}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.333256, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 76.667, "width_percent": 23.333}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C**********154%7C2%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxoRGEwaDJORTA3MW9EVHVoblRySEE9PSIsInZhbHVlIjoiRE9VRlBHai9NdGk4dDFqQW5FUkZ1UTUzcjBMenBwbHJLeFBsU3VKYnlQYW5Cczc4ODRvd2FiakQ0bi8ydmh6ZjVMclZEL1ZVNjY4NlVwN1E2RDVOTW0xYU9nZHdROGRzUDN1VWM2NEhESUV2cEpGcW8zcElLMitCMVRXbFUrbCtySHJrdTVyRlZodkRMc0Zoc3lNZUxySmhMUlZXV3dpd252TzNQZ0lNMXMyWGVMSE9FekRUV0kybHE4ZGQxNTB1eHFoMkF2Q3o2eGVWMkdzaWU5QXU3di81RzNaZ0tlYllNNVI1WmsrNE4xcnlWMTl0L0Y2dFdEalUzMGhGQi80dG95cmYyaXJHc25IU2xBQTNzZzdtb0JWeUQ0Mk80YllIbllhTGNuMUNOcE00TWxCdGJ1cDgvaE8xeWM0K1BWMC9FWHhLSE9EUVpaMGJENFhEYlVJTmpPTWwvSFI1YVRCcERmK3hHaGNubzBUOU9EV004Y3M4U3UxOUZ2WXVyQ3EwazVIVzJMQk1aY1FTb1FRemRVVUZuRitHMXJJL2N2VDJFMnpPUmZVZU1tVXJpVXdVVzd0V1BaWCszL0JkR25sVFBxNEU0c2NjcTlEZ0dPNnNnYTJmYng5QmZvZDhNRXJEaVd4Z2JVdVljZnB2a3cwbm14b3ZaZTJlR0dvSm1NckUiLCJtYWMiOiI3OGQ5M2IzODRhNGRkMzZkODZjZDIwMzEwNThhNGUwMTFkMTk2MDcyZjdkYWFhNzA1ZTE0NDY3MGJkMDk4OTY1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImEzekVKdDhsU2xxY2RybWJzbEpqY2c9PSIsInZhbHVlIjoiRWhMYzNsWXlSeWcwbVB6OU96a0tlNll4OStqVyt1c2c0R3lwWTJBdXRvVFV0N0RQaGZ2R3VDSWlVdVlEdCtWeEEzVjFXNW1LRUVFcVcyU0k3RkNMZHhoVVh1dWFKT1hlZWJiQWhqZkZFY1VySW9zVlkwZUR5YnN2c2Nvd24xbFJTYlNwUGdraEdvaU1RZkZTTWlxZ01PMUowSmc3S05wbFRZY1dmS1BCc0FHWXc1dkxlK2RGNlJJR1NLbndPVExzVUlCU0ZqK1N0QzFiRHpZL1o0enpQeXBLN01BTkVWNUdlRDYzSjZDeHZUWDlJQWk4YUtqNzlMRXA0VFRteXp6dWpZUjNtaVNOVEs3Vnk4M2p5WXdMU0xjSFpXdXR6dVlXa1ByNy82OXhZd1NYenBvN2JtOElJT1dLdnRWVkwyQ1dNK21KbXE0SnNITjQ5UjNsYWQxQ2p1cG5qNzBsNzNYUkpxSHorRUpRMlF2dkZuRXU1WTM2QnlNSG9WUjlRbXBCdHFFWTNYWVhDMlYrakp2TVc0N1dXMG0zY3crR1NEVTJzRTlSZTRiWSsrWVVHNTJZNklxNTIyQ0JrNm5LYVFFVmttNmxoNHl1YnV3cFowNkg2NVRFOWxnNkpHckd0ZE9BcjRMb3RrNVZRRUhNSVM0RHVlZFdyb0s4Z1lwcEZIUjIiLCJtYWMiOiI5YTQyMjRhYjkwM2MzZWE3ZjJlODEyMDNkNGYxZjVlNmJkMzkwY2U0ZWJjOWFmZDEzZWZhNDZmNDQyMzhlNDQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1651527570 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Cu3UyOJpq8Q80cZEpugXvVrolpBjlhoa4viY2usT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651527570\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-385101512 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:52:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRpcTBoTlFEMXpiN2FVUkNIa21JZlE9PSIsInZhbHVlIjoiaWZyS3dOcUFjQlo1cllJOUcxVk1VeGpmRkt0UlU4SnVjdG1DQ0tuYkU5T1ZCK3B0QXJQMGhQZE91T0x0T3JZU0NRVzNRSmxaSEF5eU44cTNSS1BmcXgweklCSVlLdFZ2NkF2UExPL3llWFZTRldLREhNNXFmclMwRVc5a1Y1eE83SHl1cTRncDJ0R1IvSDI3Ym1kVFA1OGtOS01hQ2wxTHZVQU5XbXNra0MyTTdQMFQyL3JIZ1Arck04SkgyM0R6KzRsK1JoampuL09mMUM3d2dkTFpQTUxVT0FFZ1JQNDU4TlBTeHBUbUh6N09pRXlDT0N0TGF2WDg4WURNQ0FqMnVvYzV0OEVXNGNUNWh6NjJ5ekEyQTdkRGkrTi9xcXVIZ1VRRE5oVkZVZ2twc3FKbkZDRGY1TGlaajQxV2psVktCWlc5dE5Xb3VxRVl0RVY4aTNaTEJvRmgrZVcrTmdWM1ZzbWJsN0hiUWpwZXZGZ3J4ejNRZWEwVFJXTWQ1M2c4K1ovRnl5NzY5R3A0UDZFMGczREp0QXVhOXFxazh0OVJBMmxhUnB1Wnk5ZHA1S0pwSHBhOW5aWllmVVhjMWdMUHhtaEZwYllId1p5d3Jjc2gvUkNRQkdkM3ZuRTQ1U1BSdnprZlZEbGUwVVZvNk1adkZPSTByS3VSMTBrRHorRnEiLCJtYWMiOiI5MjAyMzY4ZDFhMjQ1MDFlMWU1YTIwMjMwOWE0NTg0ODA4OGIyN2FkODY0ZDdkNGI5OWRhZmFlMWQxNjA0MTMwIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:52:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9IRkRCS3NSQUtnVEJMQVh4NE9yTnc9PSIsInZhbHVlIjoiUHhqVU1PYzJ4Q2R1WDg0SlpFWkZqeTlNcjg3YVJXeGpOSGFmQlMweUJHUGI0Q0FCcjZ4ZDgrZmJ2RVJDaHh4QlphMUxTQ29lWndCRUtLcTh0RnlZUTdzZmVxSnQzZG5wQ05DaHdnUGpuY2lnN1hPOHhGL1RMb1Yxb0FSbXJORVBtTUgrQmdpWjloR2hUT0lzR24rbjNSZnJNQm9mWDkvY2pCMDZWd0tZclVMQVNYNUVHa205VWtQY2dKa3BOZHFHYWJLNTdpNDV3bUFuaXZ2RENRc3kyTDVaTTM2ckJaajZsT2VKNjQwWUlTNEtOSHdxN1BLTkxnZ0NpVGhtOWVxeEJEMWRQSjZLeDMwVG0zQ1krS0tEWDFVNkk2YXpCa0JRaFJza2xIUmpGdDZ3S0tKT0llRkhDMzBmK2pybTZhYi81S0Uwdm1tbWw5SGFSOWh0d2pHQjJoRUVWVmFabVE0bW9ycUFCdnNITWRwckoxUEJOUkFVMkhDbmJHNThKMk9LM1huNzdvZzlla2M5a21sZ2kzN1RWamNIN1Y2L2R3UkVsN1kxY2JBOGNqc0hLUjFua21ZdGxxRTdXVERhMUZEaCtzTm5uL2FHQytLU2hwc2FwWEZzUUxKSG02T3V2cjNtUkVIejIzWEhVcUtKMXUyZGJ4c3R3djhpM0NCUkdzeGciLCJtYWMiOiI0ZWUxOGZhMGQ5YzhkNTczNTkxNTA3ZWE0MmU5NjAyMzU2YzE0MzQwY2UyZjNmMTU3YmQwOGQyMDI0NzU4ODg0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:52:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRpcTBoTlFEMXpiN2FVUkNIa21JZlE9PSIsInZhbHVlIjoiaWZyS3dOcUFjQlo1cllJOUcxVk1VeGpmRkt0UlU4SnVjdG1DQ0tuYkU5T1ZCK3B0QXJQMGhQZE91T0x0T3JZU0NRVzNRSmxaSEF5eU44cTNSS1BmcXgweklCSVlLdFZ2NkF2UExPL3llWFZTRldLREhNNXFmclMwRVc5a1Y1eE83SHl1cTRncDJ0R1IvSDI3Ym1kVFA1OGtOS01hQ2wxTHZVQU5XbXNra0MyTTdQMFQyL3JIZ1Arck04SkgyM0R6KzRsK1JoampuL09mMUM3d2dkTFpQTUxVT0FFZ1JQNDU4TlBTeHBUbUh6N09pRXlDT0N0TGF2WDg4WURNQ0FqMnVvYzV0OEVXNGNUNWh6NjJ5ekEyQTdkRGkrTi9xcXVIZ1VRRE5oVkZVZ2twc3FKbkZDRGY1TGlaajQxV2psVktCWlc5dE5Xb3VxRVl0RVY4aTNaTEJvRmgrZVcrTmdWM1ZzbWJsN0hiUWpwZXZGZ3J4ejNRZWEwVFJXTWQ1M2c4K1ovRnl5NzY5R3A0UDZFMGczREp0QXVhOXFxazh0OVJBMmxhUnB1Wnk5ZHA1S0pwSHBhOW5aWllmVVhjMWdMUHhtaEZwYllId1p5d3Jjc2gvUkNRQkdkM3ZuRTQ1U1BSdnprZlZEbGUwVVZvNk1adkZPSTByS3VSMTBrRHorRnEiLCJtYWMiOiI5MjAyMzY4ZDFhMjQ1MDFlMWU1YTIwMjMwOWE0NTg0ODA4OGIyN2FkODY0ZDdkNGI5OWRhZmFlMWQxNjA0MTMwIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:52:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9IRkRCS3NSQUtnVEJMQVh4NE9yTnc9PSIsInZhbHVlIjoiUHhqVU1PYzJ4Q2R1WDg0SlpFWkZqeTlNcjg3YVJXeGpOSGFmQlMweUJHUGI0Q0FCcjZ4ZDgrZmJ2RVJDaHh4QlphMUxTQ29lWndCRUtLcTh0RnlZUTdzZmVxSnQzZG5wQ05DaHdnUGpuY2lnN1hPOHhGL1RMb1Yxb0FSbXJORVBtTUgrQmdpWjloR2hUT0lzR24rbjNSZnJNQm9mWDkvY2pCMDZWd0tZclVMQVNYNUVHa205VWtQY2dKa3BOZHFHYWJLNTdpNDV3bUFuaXZ2RENRc3kyTDVaTTM2ckJaajZsT2VKNjQwWUlTNEtOSHdxN1BLTkxnZ0NpVGhtOWVxeEJEMWRQSjZLeDMwVG0zQ1krS0tEWDFVNkk2YXpCa0JRaFJza2xIUmpGdDZ3S0tKT0llRkhDMzBmK2pybTZhYi81S0Uwdm1tbWw5SGFSOWh0d2pHQjJoRUVWVmFabVE0bW9ycUFCdnNITWRwckoxUEJOUkFVMkhDbmJHNThKMk9LM1huNzdvZzlla2M5a21sZ2kzN1RWamNIN1Y2L2R3UkVsN1kxY2JBOGNqc0hLUjFua21ZdGxxRTdXVERhMUZEaCtzTm5uL2FHQytLU2hwc2FwWEZzUUxKSG02T3V2cjNtUkVIejIzWEhVcUtKMXUyZGJ4c3R3djhpM0NCUkdzeGciLCJtYWMiOiI0ZWUxOGZhMGQ5YzhkNTczNTkxNTA3ZWE0MmU5NjAyMzU2YzE0MzQwY2UyZjNmMTU3YmQwOGQyMDI0NzU4ODg0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:52:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385101512\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1715301507 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1715301507\", {\"maxDepth\":0})</script>\n"}}