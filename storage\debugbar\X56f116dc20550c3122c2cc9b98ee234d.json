{"__meta": {"id": "X56f116dc20550c3122c2cc9b98ee234d", "datetime": "2025-07-21 00:52:18", "utime": **********.497103, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.774662, "end": **********.497133, "duration": 0.****************, "duration_str": "722ms", "measures": [{"label": "Booting", "start": **********.774662, "relative_start": 0, "end": **********.329324, "relative_end": **********.329324, "duration": 0.****************, "duration_str": "555ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.329338, "relative_start": 0.****************, "end": **********.497136, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019139999999999997, "accumulated_duration_str": "19.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.417819, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.309}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4335709, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.309, "width_percent": 3.135}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.46, "duration": 0.01427, "duration_str": "14.27ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 25.444, "width_percent": 74.556}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C1753059091919%7C1%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijl4K2xMd0crMGs5NCtDZlF3RVlqVmc9PSIsInZhbHVlIjoiUU0veDZVeG5ScWViU2thZmtxRFBwUFN2K1p6TnpkMkJNMWZoQVhXUUExN0gxcjgyT1RmdGZ0Q3hmS0prQXFXemFldEpxY3VsaHkyYU0zL0JxYTV2cGFyMDNIQjcrQWcrZjNHL1ZLeDhPRW1iMjhBRk9tTnB2SUpMbnNmcEJtYWNVT2NSRXlXczg1ZVZ3cjAwOXJpendKZ0phcHR3SkdrdnJrL2JORVcvc24zcFBSRENvMzJuMmhrVDZiWUMvb0NyLy91MVFYYWIraUhqemV3ZWhVNlF6amZRN2pmRDU3V2NSTFBObjhVVmMrQUR3TjhDRVdEYS9CWDRiaUc0d2hMU25zYTY1MCt1U092R2NTdE84NU02cUpjTW1LOUZyUHNiOHN0cmZ4T296RTZTS0dYeDM3QVUvcjdQbXpZWW9HNXptRnEvVHFBVDhMVGFnQWlYZ3BOcUtjNXdIVHpaQ0NyVzM4RlZnTEt2N1ZQcWd4SUNmMjdlcFZSZGd2RjJNQ1cvZnFUT3JMdHVDM3luSy9BVFd1dVAxVWZtMVY2bGZ5WjAzRmQwdmFTRWZ1UHhSTWtmVlZ3aC9UZVhvSTFGR2tRQTJXT2RPdGhTWkt0UnlHRlhxZzREbmhuS09lTW9EUnA1S291alFUNkVDZXBKSDkxU29SRWRURGJzREFGRU9rWVQiLCJtYWMiOiI5Nzg1ZDM2NjdmZGQwNzIxOGM4ZWJmOTJmNjUxMzdhMDI0NzcxZDIwZDZkODgwOGRjZDk1NjQxN2RkMDBmZTdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik43UzJOY1R5V0VLalBINDVvNUpCTmc9PSIsInZhbHVlIjoibXUvU1p2VDZTR2gvdjNNd2JycWdCUmdPU2FPdDVpbUVlZE9PbDRRaEd2eUlFS0xCSnJ6a3FIQloxaXE4YVd3YnVPM2ZMRDBTd1N3akxiMnBGU2RiRjNhYW9RYkFWaldKMWd6cjdqMVJvbnhvSnZ1bWwrVUc1NVduRFV2cXpBSkI0a1N5eDA2dTBjRGs3UHdER0tQU1hONENRR1B2b25hSTdJMkJSSEhkdHJYRTFLRWxZbStFVEc2SVlZalhVVkZEbUFjTkRZRzI5aTZQZGRWVkFVL3MvSzE5UlcxYnIvdGIrTytTRXA5aldBUjE4anBMS3VxM1IwdGhLQmV3c3lxbkdLZFMzeG5KWHFYc2pmNVUwZFl1MGtsUWQvcm9RNFhCOExhdEZiZnJ2N2R0Q3pmV1UrQ3QrY1hOb1N5a2FmU2ZtajBWZjRQcjR2dDE3bXNXcmk2alBsWlB4Ly9Uc3pWYnlBUDdHVU9MVTIyeE1zK3ZFVlo4Sk5vMU5RamF0Vlh4MjBwL0c4MFpMVWRPR1VwVHpjcC9MQ1pTYWhTTjc4eEVxT0NPR2RaOHlwN1M2MWF5Rkh0SEJCZGRiWFhubyt4WmszQWJKOG13bDRCYkgyNU84UStLUFpEclc3UUdmQWN4Vkd1bHQ4OVdQNlkrK2xxSmVNK05QU2tVdUMzK3gwbzMiLCJtYWMiOiJlYTQ1MTY2OGY5NTFjZWNlODBlYWNhODVjMDM2OTg4MzkyNzJmYWZiY2ExYWI0YmY2OTExMDI4MzdhZDg0ZTgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2015477942 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Cu3UyOJpq8Q80cZEpugXvVrolpBjlhoa4viY2usT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015477942\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1081314978 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:52:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9mUnlqdmJJRFdqOUhVV1pYSXh3OXc9PSIsInZhbHVlIjoieFBJRVF4eEVnY3JyMXRWTEhWbWQxV0tPTFdqM1ZwVWpTc1BJbUZsWjNnUEFEbkQxeG93K2Y0VG5ISU5Sb2VnUitJcjhRYkxUMWFvWkU3TG4rZzkzbERsRExmeVpMVUd2YW4zc0R5dWFVelJRTlA5TFVuQjlsc2VaVVRlc3lSTmE5aVFDWnhHRUVING1Rb0xCSm9UNlRnVTZNT1BOTGY1em4wZVlicWdsSFMxWThFWm1xekZ3ayszbjRMV2hiTDdLTGVWOHJ4QTJVWllGTklBT0FQaFo4SmFaMEJGOExycUdVbXc5VGd5R09tQTlSRFROMmVMMTFWTDcrVWNWbi9wOUJkQldIOFRYeCt6a0wzd0ZSMWYvL29XNmVWOS9vdXRDQjFPUXFRNzRwNms1bTJ2a0dyckRORmtvM2FEc0pMc2gwbmxBaVZVNzZockg2RGlGMVNEeWU2aG1zUUE4UWZaTWdiUXYvZnU0TzJ4NmJCNjNnZG82Z1JOL3hVWC9wOVhZTXhHRE1WaWRLQTlIbFZXdUh4Q0l6NTNXajdVMkVKYVN1dDU4ekZUMHJLZW9jMExuVVloMkVmSDliVXFzY2kya293bU1GWXhRU0ozZTk4UG9rQS83NlNMZitTZjZPcmplTm1yWGNPZ0FQM2svcldGN3JnNUZraVhDMFpWY3hzcEkiLCJtYWMiOiJhMjBjMmZhOTNkMTc0NDUxNTllNDQyN2FlMDA4ZGFjYWRjYWYyZjM0MjcyNTU1YmE5OTE2ZWU3NjZkN2I5NTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:52:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVyTGpWRHllV3lZVHc0NUJIWnlmMUE9PSIsInZhbHVlIjoiemlnYm1FdVlablpERzU2MHBoZ3NiKzZ5TTdpNmVqcEdmTE9wMjFRTG0zTkRlSlVDejhtSmxqM2NnQVBqK0xsdFlCQ2hpd1VJcHVHdXIxZ0l1eEc5N3d6NC91YzJnQ3lwZ000TmVjbFZBUE02MS9SekN5Y25ONURTaDI5V3hkVDIvWEpOSVh6MHpNbmZNZ1FBekxBZnhHbDRBbWF3ejFRRklZbmdHL21LTkd1NUd1Z0JZNUlYa3VEWTBOQ09ERUp3NFROZmF1am54Rk8rOEJTRXFaSmNaMUZFTzlEZDJxWDlWcWpwOG1tQ0lYL3pjL1F1U2FqTW05WFBBKzhWWjR1ZHJxd0JVV0ViSFNPZG92ZERIaVBza05oWFBZSjNhcEc4VVVjVFp1bVlPdzlvMUNSVUFVMUF4UXVzTnJ0cTlHR2prT25GejZVWUxDK29VL2tuT3Fnc3BDUU1jQ0xhM0JOVi9IZXFxVnJQMDR5NVZYVnFiSzVRWVhGQU5lQUp5dHQrTjNJLytxK0hiN0tvcU1Ya09IS2J2R2x6aHJkSEJkQ1hRYU9YN0xPY2Z0QitGUk9uMDhrek41MnNoUlpTN01Kb01veW5CLzZkTDU4bGlOd2Z6bHdTSmoyTm5qU1AwN2drN1NpV1FFWUZOaTNyTG15d3d0QkFlcGY4ZWgvR3pkczQiLCJtYWMiOiIwMmY2MDVkYzYyNTY0YmM5MjA1ZjQ3YTE1NmZiZWFjZjE5NzU2ZGViNmIyMjRmMjIzY2IyZTYyZjhiNmQ3MTc2IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:52:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9mUnlqdmJJRFdqOUhVV1pYSXh3OXc9PSIsInZhbHVlIjoieFBJRVF4eEVnY3JyMXRWTEhWbWQxV0tPTFdqM1ZwVWpTc1BJbUZsWjNnUEFEbkQxeG93K2Y0VG5ISU5Sb2VnUitJcjhRYkxUMWFvWkU3TG4rZzkzbERsRExmeVpMVUd2YW4zc0R5dWFVelJRTlA5TFVuQjlsc2VaVVRlc3lSTmE5aVFDWnhHRUVING1Rb0xCSm9UNlRnVTZNT1BOTGY1em4wZVlicWdsSFMxWThFWm1xekZ3ayszbjRMV2hiTDdLTGVWOHJ4QTJVWllGTklBT0FQaFo4SmFaMEJGOExycUdVbXc5VGd5R09tQTlSRFROMmVMMTFWTDcrVWNWbi9wOUJkQldIOFRYeCt6a0wzd0ZSMWYvL29XNmVWOS9vdXRDQjFPUXFRNzRwNms1bTJ2a0dyckRORmtvM2FEc0pMc2gwbmxBaVZVNzZockg2RGlGMVNEeWU2aG1zUUE4UWZaTWdiUXYvZnU0TzJ4NmJCNjNnZG82Z1JOL3hVWC9wOVhZTXhHRE1WaWRLQTlIbFZXdUh4Q0l6NTNXajdVMkVKYVN1dDU4ekZUMHJLZW9jMExuVVloMkVmSDliVXFzY2kya293bU1GWXhRU0ozZTk4UG9rQS83NlNMZitTZjZPcmplTm1yWGNPZ0FQM2svcldGN3JnNUZraVhDMFpWY3hzcEkiLCJtYWMiOiJhMjBjMmZhOTNkMTc0NDUxNTllNDQyN2FlMDA4ZGFjYWRjYWYyZjM0MjcyNTU1YmE5OTE2ZWU3NjZkN2I5NTQ1IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:52:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVyTGpWRHllV3lZVHc0NUJIWnlmMUE9PSIsInZhbHVlIjoiemlnYm1FdVlablpERzU2MHBoZ3NiKzZ5TTdpNmVqcEdmTE9wMjFRTG0zTkRlSlVDejhtSmxqM2NnQVBqK0xsdFlCQ2hpd1VJcHVHdXIxZ0l1eEc5N3d6NC91YzJnQ3lwZ000TmVjbFZBUE02MS9SekN5Y25ONURTaDI5V3hkVDIvWEpOSVh6MHpNbmZNZ1FBekxBZnhHbDRBbWF3ejFRRklZbmdHL21LTkd1NUd1Z0JZNUlYa3VEWTBOQ09ERUp3NFROZmF1am54Rk8rOEJTRXFaSmNaMUZFTzlEZDJxWDlWcWpwOG1tQ0lYL3pjL1F1U2FqTW05WFBBKzhWWjR1ZHJxd0JVV0ViSFNPZG92ZERIaVBza05oWFBZSjNhcEc4VVVjVFp1bVlPdzlvMUNSVUFVMUF4UXVzTnJ0cTlHR2prT25GejZVWUxDK29VL2tuT3Fnc3BDUU1jQ0xhM0JOVi9IZXFxVnJQMDR5NVZYVnFiSzVRWVhGQU5lQUp5dHQrTjNJLytxK0hiN0tvcU1Ya09IS2J2R2x6aHJkSEJkQ1hRYU9YN0xPY2Z0QitGUk9uMDhrek41MnNoUlpTN01Kb01veW5CLzZkTDU4bGlOd2Z6bHdTSmoyTm5qU1AwN2drN1NpV1FFWUZOaTNyTG15d3d0QkFlcGY4ZWgvR3pkczQiLCJtYWMiOiIwMmY2MDVkYzYyNTY0YmM5MjA1ZjQ3YTE1NmZiZWFjZjE5NzU2ZGViNmIyMjRmMjIzY2IyZTYyZjhiNmQ3MTc2IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:52:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081314978\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}