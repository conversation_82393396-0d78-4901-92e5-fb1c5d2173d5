{"__meta": {"id": "Xc2d8b1a6e85516df99b3a25e5c4c33a2", "datetime": "2025-07-21 00:56:39", "utime": **********.544483, "method": "GET", "uri": "/change-language/en", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.123395, "end": **********.544503, "duration": 0.4211080074310303, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.123395, "relative_start": 0, "end": **********.470108, "relative_end": **********.470108, "duration": 0.3467130661010742, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.470117, "relative_start": 0.3467221260070801, "end": **********.544505, "relative_end": 1.9073486328125e-06, "duration": 0.07438778877258301, "duration_str": "74.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46219144, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET change-language/{lang}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\LanguageController@changeLanquage", "namespace": null, "prefix": "", "where": [], "as": "change.language", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FLanguageController.php&line=17\" onclick=\"\">app/Http/Controllers/LanguageController.php:17-51</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01908, "accumulated_duration_str": "19.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.499379, "duration": 0.0136, "duration_str": "13.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.279}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.521074, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.279, "width_percent": 2.044}, {"sql": "update `users` set `lang` = 'en', `users`.`updated_at` = '2025-07-21 00:56:39' where `id` = 22", "type": "query", "params": [], "bindings": ["en", "2025-07-21 00:56:39", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/LanguageController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\LanguageController.php", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5240629, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "LanguageController.php:21", "source": "app/Http/Controllers/LanguageController.php:21", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FLanguageController.php&line=21", "ajax": false, "filename": "LanguageController.php", "line": "21"}, "connection": "kdmkjkqknb", "start_percent": 73.323, "width_percent": 16.614}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('off', 'SITE_RTL', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["off", "SITE_RTL", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/LanguageController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\LanguageController.php", "line": 41}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.528787, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "LanguageController.php:41", "source": "app/Http/Controllers/LanguageController.php:41", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FLanguageController.php&line=41", "ajax": false, "filename": "LanguageController.php", "line": "41"}, "connection": "kdmkjkqknb", "start_percent": 89.937, "width_percent": 10.063}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/change-language/en\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "success": "تغيير اللغة بنجاح.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/change-language/en", "status_code": "<pre class=sf-dump id=sf-dump-638803310 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-638803310\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-746651430 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-746651430\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1095505157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1095505157\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1828117465 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753059391774%7C3%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InQ0OEN0azd6dm5EL1pLRmROUFduekE9PSIsInZhbHVlIjoiaW5kTSt5YVRXTC9NT1U3MEZISDhleURaZGdGT2lZQUNXbWVabUNSdTVuRVIvV1lxZko0cHVrZW93SlBHZ1BRM0s0TGdYMElpVjYzbDBIaElucS9WMVc0MThLN3Q2a2QyL05YM25CTklBc3pPYm5XbDdkalE0Sjd5VjFtNXVFaWM2ektEMTlvMWJxSHpxYVczclY5MFlwbXB2SWJxVFdwZGNjd3FqSVNLMkx0b0VROWJiTGJmaU9Jdmw1cW1kOGllN1RXWHdqbVR3alVzeEVjOWw1UHhwVVZOMWdUd2Rvd1YyaUttQ05sWXh4dVpqVTVxLzc3UjF2YkJ6TU0zak9hNEUzWVk1a0tVcTlGeWRDZHRDTWRnVXR0dlRmRi9ienJiV1kzRVNJTUprSWh0ZjRrMHlUOElLMU9CekdVNmRXSEROV1VHYjZnbSs3dWJDc0hadkk3MDlaYnl0eHNDWGpvby9KRjZjOTdkUG50VFRLZ2FUTDZBUER2Lzg3dWJlQjNyRldzZEdTRHJaZ3VpbjdnSkkwR0tBR0dieXp0djFpb1lHTkxLeGJZVXlZMGllUVpTR2pkSnE0UlBXYzZHb0pibkhVMDZybEZjMHNiaUVyd1BPL0l5YUhmVUJtZXZVR2JqUlVMY0daOHhRZEIrbWgrRmIwUDJhZnNzeEdoN1dDUXciLCJtYWMiOiJlNDVjZGM0OGE0MGNhNjIzOTRkYjk2NzI2ZDY4MDJlMWM4Mzg1MzIzMWIzY2VmYzI2OGUyZmNkMTFjNzI1NzYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdZRGV2K3Rra0NzZGZKU0JaeFMzZWc9PSIsInZhbHVlIjoiREdkUE9UNzV0QnV3bUN4ZTFEUk9HaEZYN2NEYkt1NkcvSStjakJ4MzlLanJpTzg4TjFIWVl5TmJmaDZpbkZFb01xa1NIUHZ6WXhZYStCZEY3VXU1b3JCdTEzSnR0Y1cvVzRmZ3NzbEVlZ3c2RllTaHhhRFM4VjdWYmxxUC82NS93L0hDSnVNZUdySmRuNHVuN3lWT3pURmlGL09PZ1I5QjUrVFRxLzRWak95UHJVSm9PcmFoeHQ5cTdVVWt2TllabXpkRWdHeU96RWVXYjg2MEovNSs0UC9FbEFYUDRHZFQyZytMUmhZRGdMRDhmK3d2ZExhWDdBQ092dEJmWTR4TEdLN3REMW5lZ2xzWUEzdjhRbmtLc29LRmVNN215SXJyZFZZTlVNbGRWeFFpYWxQYnA1aWxaUDJwQlQ1YnhGQTg3Z0M0ZmphNzF4dWVYQ0ZZb0NrY3huVldmZ3F4aHdhRzVtMERYQnJ5QVFISDJWbG5SRk90UFFMeFJ6ZmFqTW1aWWdvTG9WRGd1d3h2KzRSSnZBUmU2OHkvSlN1dzFYRnV3SUQwMmdKU1NHeXRDcVVvWFZ5Y0g3blM5NUFFTFBwTUdvTTllbXJFTm92bmxiZ0tpQnVjREhUUkpBeHQ0Q2Q3bkpFY0dmcDd3MVNBMWtObnRuU3JsODErcGU4YWJGcG4iLCJtYWMiOiI2YWRjNzQ5ZDU4ZDZiYzNiM2M5MGE2ZTFjZWQxZjcyY2ZhNjMxMGU4MWU1NWY2ODE3NzY1YjE5OTQzZGQ0MWRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828117465\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-113533230 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113533230\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1561439760 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:56:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InN4eE0xMWkrdnpGd3Z4MldoOFBtU1E9PSIsInZhbHVlIjoiZFl5WGM3aVErWGhlM05FOXBORVB1dnJHN2wrSDMyeE5Vc1JVNFJrdVRFeHhhR2pKWTEzVDU2Nk44eWRFb2FOckVwaHhNdmQyNUhtb05tWnpQUUhrWmRCSGNzNmVDSWcxaU9icEZUYWZCMkFUQkwyaUF4bU5ZdjdaT09ST01Camo1S2QwQWlKdnF3L3ZrZnZ2b0haTEhZU1hMNTN1dXNiWWQ5aW85VTZPZ0VraUV5MEVuYUowN3J6cWE3dEtOWm5QSW5aOGhhd2kzaU1hTXBneDFWckJvbThCbnkzdytjSEQxL1pjZmpGWk5KNytIV0d4MVdvVHdqUEZ4Z0FCNUZBb3V5NURaNDA5U0xWMHd1NW5UTnkzNmNYRU5KQnFYRGhzK0QzNEhncTUraDg1ZXF5eURFZzBXK2RtTFZZbC9XdDVBbVhSWUJtRXVTTkVKZjJ2U3FoRlIxTDNiQWVtSU9uMWZtNVdkc1lVemY4MDVYUGVSdDdRUFdwTUI3c21TN05raXRNTEdpZWJnR1NpL2xOUE85T2VWSWhkSWwzYUhwd3JhTUduZklQRk13MHUzV1VxNlJFZCtpYVlaUHFlZ2h1ZittRysvL2xWMmZWaUpJaStLa3ZoWGg4QVlPQ2pNV3hpVjA2bURuaFRRQVFUMGZDZ1Jrb0M4S3pNMjFsNitXakYiLCJtYWMiOiJiYTc0MDg5YmZkMTkxODY2NTk2YmFhOWViYTIzOWQ5MTAyYzFlYzQ0NTJkMDEyMDAyYTdkODJiNTkxMzU2ZWQyIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ind4bkYzVVpPUGpWS1I3L0VteGhOY3c9PSIsInZhbHVlIjoiZmhCb1h5bjhJeWoyZUkvbG9zalc3OTM3Z094ZDFRUVFsR3ZSenQ5WVN0WVQ5cWNaV2R6a2NPY3BBdW9UTzVPTktmdG91NE1IT1VMZmdPWW92UmdMRE9UVFJiYzl6c2UyMUdwTnFFbWdaQzFPL2R6ckJHWDRwenRZU2txR1lHVHhpaURZQ0FYK2E4b2t1cUtVN2ZoeXJOT0xmSHBpYzEwNEc3djF2ZFl1UmhwZUNSKytBR2w1Z3BkaEpITVVncEZTeWVQQnE2WjB1T0RFa1AwUVBpQXIreUJMNm9vNTVka1JoQW52SE5jYVc2VDZnTFVRaWxneEowVDIybmY1NmE5NFZUYzNHTm51NklqTGxhLzYwdEMvMmVWN0tEYUJUbFZSd0xjZHJFRVlSTXJVODlFaGxjVXU3V20rMFMvTEdzUUpxd0FlVXFOTUZqaVhMRk1peDdrK0t5ekFQSytHY0FmNTI1blpWWllpdVllNlVscGQ1TXB6VUpsLzJacDNINk5Fb1pIQU9CYSs4YldPKzhaWG1lYzc4OEdxdmRDV29nTVlFazIwY1dYVG5Pb0VUTUpMQWdWb2k1TlZxQjVXODdra1h4bGVlR09tU1RVbGMzQmV2dVM0ajl6VHRxMGRJbXJEMDdQRWJaVTkwb2JOVmNzd01mWE5YVDRDUnl4SFQ1S2siLCJtYWMiOiI1MTJmZTIzYzZlNGE3NGRkNWZjZDdhMjNmNTdmZTc3MmYzZjRlMWZlYWM2MTQxODljMTA1YWQ5NGZiNDYwNDRjIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InN4eE0xMWkrdnpGd3Z4MldoOFBtU1E9PSIsInZhbHVlIjoiZFl5WGM3aVErWGhlM05FOXBORVB1dnJHN2wrSDMyeE5Vc1JVNFJrdVRFeHhhR2pKWTEzVDU2Nk44eWRFb2FOckVwaHhNdmQyNUhtb05tWnpQUUhrWmRCSGNzNmVDSWcxaU9icEZUYWZCMkFUQkwyaUF4bU5ZdjdaT09ST01Camo1S2QwQWlKdnF3L3ZrZnZ2b0haTEhZU1hMNTN1dXNiWWQ5aW85VTZPZ0VraUV5MEVuYUowN3J6cWE3dEtOWm5QSW5aOGhhd2kzaU1hTXBneDFWckJvbThCbnkzdytjSEQxL1pjZmpGWk5KNytIV0d4MVdvVHdqUEZ4Z0FCNUZBb3V5NURaNDA5U0xWMHd1NW5UTnkzNmNYRU5KQnFYRGhzK0QzNEhncTUraDg1ZXF5eURFZzBXK2RtTFZZbC9XdDVBbVhSWUJtRXVTTkVKZjJ2U3FoRlIxTDNiQWVtSU9uMWZtNVdkc1lVemY4MDVYUGVSdDdRUFdwTUI3c21TN05raXRNTEdpZWJnR1NpL2xOUE85T2VWSWhkSWwzYUhwd3JhTUduZklQRk13MHUzV1VxNlJFZCtpYVlaUHFlZ2h1ZittRysvL2xWMmZWaUpJaStLa3ZoWGg4QVlPQ2pNV3hpVjA2bURuaFRRQVFUMGZDZ1Jrb0M4S3pNMjFsNitXakYiLCJtYWMiOiJiYTc0MDg5YmZkMTkxODY2NTk2YmFhOWViYTIzOWQ5MTAyYzFlYzQ0NTJkMDEyMDAyYTdkODJiNTkxMzU2ZWQyIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ind4bkYzVVpPUGpWS1I3L0VteGhOY3c9PSIsInZhbHVlIjoiZmhCb1h5bjhJeWoyZUkvbG9zalc3OTM3Z094ZDFRUVFsR3ZSenQ5WVN0WVQ5cWNaV2R6a2NPY3BBdW9UTzVPTktmdG91NE1IT1VMZmdPWW92UmdMRE9UVFJiYzl6c2UyMUdwTnFFbWdaQzFPL2R6ckJHWDRwenRZU2txR1lHVHhpaURZQ0FYK2E4b2t1cUtVN2ZoeXJOT0xmSHBpYzEwNEc3djF2ZFl1UmhwZUNSKytBR2w1Z3BkaEpITVVncEZTeWVQQnE2WjB1T0RFa1AwUVBpQXIreUJMNm9vNTVka1JoQW52SE5jYVc2VDZnTFVRaWxneEowVDIybmY1NmE5NFZUYzNHTm51NklqTGxhLzYwdEMvMmVWN0tEYUJUbFZSd0xjZHJFRVlSTXJVODlFaGxjVXU3V20rMFMvTEdzUUpxd0FlVXFOTUZqaVhMRk1peDdrK0t5ekFQSytHY0FmNTI1blpWWllpdVllNlVscGQ1TXB6VUpsLzJacDNINk5Fb1pIQU9CYSs4YldPKzhaWG1lYzc4OEdxdmRDV29nTVlFazIwY1dYVG5Pb0VUTUpMQWdWb2k1TlZxQjVXODdra1h4bGVlR09tU1RVbGMzQmV2dVM0ajl6VHRxMGRJbXJEMDdQRWJaVTkwb2JOVmNzd01mWE5YVDRDUnl4SFQ1S2siLCJtYWMiOiI1MTJmZTIzYzZlNGE3NGRkNWZjZDdhMjNmNTdmZTc3MmYzZjRlMWZlYWM2MTQxODljMTA1YWQ5NGZiNDYwNDRjIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1561439760\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-761028824 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/change-language/en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1578;&#1594;&#1610;&#1610;&#1585; &#1575;&#1604;&#1604;&#1594;&#1577; &#1576;&#1606;&#1580;&#1575;&#1581;.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761028824\", {\"maxDepth\":0})</script>\n"}}