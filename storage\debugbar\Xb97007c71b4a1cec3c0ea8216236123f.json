{"__meta": {"id": "Xb97007c71b4a1cec3c0ea8216236123f", "datetime": "2025-07-21 00:56:27", "utime": **********.655237, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.134803, "end": **********.655256, "duration": 0.5204529762268066, "duration_str": "520ms", "measures": [{"label": "Booting", "start": **********.134803, "relative_start": 0, "end": **********.576078, "relative_end": **********.576078, "duration": 0.44127488136291504, "duration_str": "441ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.576086, "relative_start": 0.4412829875946045, "end": **********.655258, "relative_end": 1.9073486328125e-06, "duration": 0.07917189598083496, "duration_str": "79.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46668960, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032, "accumulated_duration_str": "3.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6126919, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.25}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.625444, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.25, "width_percent": 21.25}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.633616, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.5, "width_percent": 17.5}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-338481866 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-338481866\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1870062355 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1870062355\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1608852521 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608852521\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-641799388 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753059218308%7C1%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdvdUpERWszT2tqZGRDekR0eDNLOEE9PSIsInZhbHVlIjoiN2R4SzAyUTRUUjRLSWRIU2djU2FNcCtvTkxjbmJtQW9oTS9FeGdZckMybFlHeWRwRFhDQld0WXJHY0Vyam5jYmpBbmhlcDdjdWU4VGRNdWF4TnVaMkJoWmhsU2ZiQUNxY0Mvc2dQeDFDREpvZFpBakhLVkF4ZW5VZGpUNUgwbGd0Ynh6VklRK3g2WmxnYWlFeGFVOFF3NWpyNWRKQnRoTVJsZUlyTTVNbjBNcExaemdoSVpDR0JhczFqNG9LQWY0dUpET3N0WXorRmJYMXlubEVBbWtwWkhTWm9SQTNBN3BvaG1xOEpaTmRBMHBhNnNYZ2xyWGlNcWhKWGhWOTNEVnpSNFFjYXErQUlvTDJPbzZ6cnJyaG5UYVY0dm56MTdkWEw1QkNjekYxU0thOVlFbytKS3hxY09XdnoyS2t3QjlqNzBCWUxmR2lFa1ZvdUpXdERKMEJJMGhuZUNuSDVXUEdxcjZoMWg3TWJUanhOQTdiZkZHY2ZQUFprOUdEdGc4NWFXcnVVZmZvUkFmY25qcytWSTJaSWl0YnhCRVg5ZHdLU1BqQTRQT0p1NGZDZm9wajFLTU56NFhBOTlQMk1EUWlJNHFFMlNTME90MThLdWFXcGt4R1BaZzVLdWxyd05oN2RSQXRaTlczaU5qUFllNlc3ZWt2NUZNL2hsWlZxY1giLCJtYWMiOiI1MWVkZDBiZDBmOGZkNWEyOTNlNDUwYzQyNGI0MmNmNWViYjI1NzUzMmIxMTBhZGRjOTBjMjdmOGNkZTRlNDUxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZsbHB0ZEpUclJCTXpFcFhOUk54MkE9PSIsInZhbHVlIjoiTzloMDd1TVp3aUczWDhhd25aNTgwUmxpV2FyYnR1c0pKRUJ1ckNHTjI0NHBmRjVudkVKcTFZZnN0T2xjdFFLL3A1eXB2T0lvSlJMMlpLOHBNTUd2aGphZkk1ZXJiZkdXNHNKOTlZaTVhay9kS1FjbUg5a1ZxYVRqSnlNbUhhU0VuWmdRTGozY0hYYnZIQWQzTjI1RGRpR0N1TnBMbGVVRzY1T0pXNnNLNk14aE9Xams2T3hNK1BhSVNDSUVncUYyU3Q2MFk4SWxjeDNRRUphbmlSdjNCemJURUhHL2FJYkVFZFNVWVdxb0NZTnA5NVIrKzMyS1ZvSFdFdmdhMDJrTVRBeTZzZklvQ3dNNHhvRWovdnJwYVFIMzh3cFYrN0NYUkVweVpDQUg5ckNjSHVUaTA5N3RrMTFTbyt3NXlhVFdlMUJQbmhNVkQ4SWRQOVhxTWpJQmNRSFZuS1RUQjJvMDZoRC92bzVsS1FBd3EvWEdSb3cwRzJTTzlBam1ISGJwcE9reTI0TmZaQW1MUjU4bmZ3WCtyUzJKZkdSSFQ0TzVFZXB3andENng5L1plRE8zRUlnM3gyUE1FZ0Y1ZjFIcFpmbjVDeGJkUGcrRldKVHhxU0hIOEY0b0tyWmI0VnFtQWRXTEtQV0diOUo0b3dKMFlSM0pDQ090dVZLdkdNenoiLCJtYWMiOiJmOWE3M2MzYzkxNzQ3MDNkMzRkYWE1MjQ3MGZmZDgwMTRmNzBmYTBkMjMzY2FjODY4NGUwOTYyMmI2ZjQyM2VmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641799388\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1556033319 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556033319\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1462070832 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:56:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImR4cEYzNlprOVBWRS91ZWhJRzc0UXc9PSIsInZhbHVlIjoibloxL21GeVVvY1R2V09EdnU0VkNPK2EvMzlXR2hkQjlwa1NwaEF1aXZOZ2Jya2JOY0ZZakR6YmhvWDVEbXBkWFVTVVpMUHovbnN0RmlRUHJYRXlORjNvZWUzTkVmMXNqSkVXTGFIL2NtejlGUnJCTTJFM2V3ejcyRytLR2VWanUzb3ZWRXkxblZMbWNDSGZBait6VThYQ1MrUmdvb0hzS3M4R3lrUXgxVHo5dWUxdnhOcW9QbmdDNHI5bzBhT0YzOU9HYnFpOGt3cWV6ZjlSazhJTzVlYnRXc3dIUjQ4V0pKZEFvc09QV3hBeTR6YmNDejNpRXlGREZPSUVYU3VKZUJPalR2TEZpeGo5NWhtYzNhSm9yOTZZRHdQcnBwVTQyTklZblZ2UGhDcWI2R2NobUdWbUVJNzlYazhBcUVuVUtXenEwelNjQUtvblFYT25kcUNielVzajk0UG5iWHdHQkNnWXlEQTdTNDVwYkJURFBvc1BoTHlCbVlyTEpZMVB2bXJDYVE2eTFUajRDbHAwZ3NGbWF5THc4Z2ZVeWl6dHZXaWllNDhLZUpaTEU1UVJHeGZwM0Y0anhiV0dlTTF0OHE4cVpBbk5FWjJjM0tORS9oTlJGY2RMc3pGZHZiOTlrVm4wWU9MU3hjeHQ4T1dhenlQbi84L0d0cGQ3RGRIeFYiLCJtYWMiOiIxMjI5MjAwYmI3MzY2ZThmNDhkNmQzMTFkYWFhMDYxN2E1NWFlNmU0ZDM5YTdiYmUzNDViYmQ5NjM3NDJjODViIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtDTlVZWWhWb0p2STdyV3NRbnNFcXc9PSIsInZhbHVlIjoiV1A2L3l4MWVwaGxxYjdudE4zeWkvNFIxOXVtelc3ODRDU0hpMXl3RXExNWNETUcrNk1GeEpoTmFKSFloR2NuT3NmREt0RkhJTGF4cUpOVGZGZi9aQ2FqT0RIMkJXT2k5MjhUd1h0TWtvelpiMUpDWVpSdTVCZ2FMN0ZsOUZYV2tzNy84c3NYSGI4aTdlMTAzWjJwbmtreXdUQnF4YTVmeFZDVStrWCtYTGFMU05wYVhxZ0JBOTVITGVVaUxGK0dsWGowMHdwWlVOdVBENHZRYmVXSXFWTEwrK05DRlFYYTJzUkc2Y1FBWDd6NTd1TVFoU09BTlBBM01OaTc1QTV6Rk9kRjFkTFJsUkRucHpTamJOdWdEbzB3NE44WGwxSGp6UHJVaXlwSjlqSkg1RTVwQmRnVnMwNkVva3FveGhnWVYvTjNuV0k1NGZNOGpXUlIyWTM0OEZnTEV2NDlZNlJYSDZGNnEvSHFtc1pFQVVsWnJIc2pvMWtLUStMRWRVU2YyS2lRTERVcFVhWmg3WmNzRVN2aHRDNVdpWnJybTI5YXNlTVB1eS8rWFFVRE9lVGlhWTN0S1cwdmVmN2tnbTNxd0czdDA4UEpzWXRWY0NLUzFpSEsvMlZMdVJlZnlKa3UrVGpocTdqTkVkdXEyb1QrNTZRVWdYa1dKK1lXSHJsaFkiLCJtYWMiOiJjYjJiYjIxMmY3Y2Y4NzJmYjYyYTI4NmQwYTg3ZGI3YTU2ZjUwNDc2NGUxZjMzODJlYTI3Mzk4OGM4NTEzMjkxIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImR4cEYzNlprOVBWRS91ZWhJRzc0UXc9PSIsInZhbHVlIjoibloxL21GeVVvY1R2V09EdnU0VkNPK2EvMzlXR2hkQjlwa1NwaEF1aXZOZ2Jya2JOY0ZZakR6YmhvWDVEbXBkWFVTVVpMUHovbnN0RmlRUHJYRXlORjNvZWUzTkVmMXNqSkVXTGFIL2NtejlGUnJCTTJFM2V3ejcyRytLR2VWanUzb3ZWRXkxblZMbWNDSGZBait6VThYQ1MrUmdvb0hzS3M4R3lrUXgxVHo5dWUxdnhOcW9QbmdDNHI5bzBhT0YzOU9HYnFpOGt3cWV6ZjlSazhJTzVlYnRXc3dIUjQ4V0pKZEFvc09QV3hBeTR6YmNDejNpRXlGREZPSUVYU3VKZUJPalR2TEZpeGo5NWhtYzNhSm9yOTZZRHdQcnBwVTQyTklZblZ2UGhDcWI2R2NobUdWbUVJNzlYazhBcUVuVUtXenEwelNjQUtvblFYT25kcUNielVzajk0UG5iWHdHQkNnWXlEQTdTNDVwYkJURFBvc1BoTHlCbVlyTEpZMVB2bXJDYVE2eTFUajRDbHAwZ3NGbWF5THc4Z2ZVeWl6dHZXaWllNDhLZUpaTEU1UVJHeGZwM0Y0anhiV0dlTTF0OHE4cVpBbk5FWjJjM0tORS9oTlJGY2RMc3pGZHZiOTlrVm4wWU9MU3hjeHQ4T1dhenlQbi84L0d0cGQ3RGRIeFYiLCJtYWMiOiIxMjI5MjAwYmI3MzY2ZThmNDhkNmQzMTFkYWFhMDYxN2E1NWFlNmU0ZDM5YTdiYmUzNDViYmQ5NjM3NDJjODViIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtDTlVZWWhWb0p2STdyV3NRbnNFcXc9PSIsInZhbHVlIjoiV1A2L3l4MWVwaGxxYjdudE4zeWkvNFIxOXVtelc3ODRDU0hpMXl3RXExNWNETUcrNk1GeEpoTmFKSFloR2NuT3NmREt0RkhJTGF4cUpOVGZGZi9aQ2FqT0RIMkJXT2k5MjhUd1h0TWtvelpiMUpDWVpSdTVCZ2FMN0ZsOUZYV2tzNy84c3NYSGI4aTdlMTAzWjJwbmtreXdUQnF4YTVmeFZDVStrWCtYTGFMU05wYVhxZ0JBOTVITGVVaUxGK0dsWGowMHdwWlVOdVBENHZRYmVXSXFWTEwrK05DRlFYYTJzUkc2Y1FBWDd6NTd1TVFoU09BTlBBM01OaTc1QTV6Rk9kRjFkTFJsUkRucHpTamJOdWdEbzB3NE44WGwxSGp6UHJVaXlwSjlqSkg1RTVwQmRnVnMwNkVva3FveGhnWVYvTjNuV0k1NGZNOGpXUlIyWTM0OEZnTEV2NDlZNlJYSDZGNnEvSHFtc1pFQVVsWnJIc2pvMWtLUStMRWRVU2YyS2lRTERVcFVhWmg3WmNzRVN2aHRDNVdpWnJybTI5YXNlTVB1eS8rWFFVRE9lVGlhWTN0S1cwdmVmN2tnbTNxd0czdDA4UEpzWXRWY0NLUzFpSEsvMlZMdVJlZnlKa3UrVGpocTdqTkVkdXEyb1QrNTZRVWdYa1dKK1lXSHJsaFkiLCJtYWMiOiJjYjJiYjIxMmY3Y2Y4NzJmYjYyYTI4NmQwYTg3ZGI3YTU2ZjUwNDc2NGUxZjMzODJlYTI3Mzk4OGM4NTEzMjkxIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462070832\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-262321645 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262321645\", {\"maxDepth\":0})</script>\n"}}