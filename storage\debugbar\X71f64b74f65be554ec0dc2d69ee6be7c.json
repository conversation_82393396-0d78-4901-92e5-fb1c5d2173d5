{"__meta": {"id": "X71f64b74f65be554ec0dc2d69ee6be7c", "datetime": "2025-07-21 00:52:23", "utime": **********.865152, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.313162, "end": **********.865166, "duration": 0.****************, "duration_str": "552ms", "measures": [{"label": "Booting", "start": **********.313162, "relative_start": 0, "end": **********.776206, "relative_end": **********.776206, "duration": 0.****************, "duration_str": "463ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.776216, "relative_start": 0.*****************, "end": **********.865167, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "88.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02526, "accumulated_duration_str": "25.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.812866, "duration": 0.02389, "duration_str": "23.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.576}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.847272, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.576, "width_percent": 2.257}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8561952, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 96.833, "width_percent": 3.167}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clck=10dm9x5%7C2%7Cfxs%7C0%7C2019; _clsk=1samuid%7C**********154%7C2%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZkSnpXbkF4a2NLdSs1ZlAxUzZHc3c9PSIsInZhbHVlIjoiRHMxTkNvSldYWmtmZjJTZTJNYVRZRzhKSzNnQWRRN3p3bWtyT2IwMnE2SEZ1Y29NRm9WRUZGNW1RWXY1RjVhay95WWJWalduMWhzV0xvUjU5aXZ3V3crOGJ1SmZZSlAxMWxmVzRkdWFnUG04dGRYNzBwWmJNRGpHMHRSMHRWWm4wcGNoOGlkbmJKQU9qK3hIMGJjbTJnUDh0elJib2ZERFlMMWt1Z3NKbUxodEZzeE9rYzQ4RmVNTkt3ZGhEakpObzlwN2VmVnhhS1R0R25nVFpyMmxUMGczUFNnNCt3cHBIZlV5cmpURW1aNTRtT0owOGt0NE1JSnhiWkxDL1pnYTFBQzhFNSs4T01vMk5OTXEwYlQwTWl3UHJwSkM2R0ZiUjhUOHZ2ZjZXUVVpdmh3Rmc3ZnpUZUZZa2N4elZBOGNRR1kyYk02VVdDcFU3RVZYZDA1dVRaQm16a2dFMkNWN25YcHRINWJyYUJYZVJtWlRCQTQxbGJkVzFhVFhYMXhnV0FzcW91MVFPSmM1c2pzOGNNOUFPeGx0VWsvYVNoUEpEY3d4Ri8wT0xkUFozdy9QQ05PUDFRTHhpN3ZRRktkNFBiSy81NTN3U25qdnR5MkduMmM4clpyQnZKeGlLQjlWUzVKYXMxZXROWHZ0OTUwc0E3R242SmFwdGxVR1RzSHMiLCJtYWMiOiI4MjIyYTRiZjBmODg2N2JlNzhjNjFlMjVmZjcwYzIyODM0ODY4NDA0NWI0MmJmYmY4NzMxYmQ3NGJlYmQ1MzU5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkcxeGNmM1UzT2VjeFN5Zkt6bHdlWFE9PSIsInZhbHVlIjoiZ1dpbnkvU2NydzY1ait0dWJFYzdZU1FZS0g4akZaK1VnWnpTeHRLSGo3R2xJSVJES0ZmRTJqMExVWG9tMFdaWlB2NHVNdUxIZVJuS05SRkJkanRGYmIybG0vNi92c1R2NDN6U2NLVzBiS08vZDNFQ2kxNnJUTTluZS9RSEdHK0JXdmdwR0lKTDdkSmZWQ2l5UUkrREVxaWE4ZEtaSGlQVXNtemFURDRoWjZNY2lmSFpURkd0QjRhRGNjL1JVVzBYSzZqOVo4Z0NJcWlXS3JpaXFnMjVXSmI3QkdqZ0c1R1RiUGRQRXVxaDI5eGRMOCtwWXJ5K1FvOHdIWER0N1kzNU8wejA0OE11WjBCRWFCUlBEZVVBOERkbTRnS3JvNHlMY01yVDgrOWtDdDN1cFBlc2YvSGh5blJnU0V1Vk5ZNVpyaWVVNHJFQXN0WDY3OGpLT3JXY0N4Nk1Kdk9lUVkwSFVUSG9oZlVDOTBDRHY2NnRlU2M0cGF1YnZNTERqc0swMGJ1T09mWmU4QzRhNXJ4cGtYVllLMkJnVzV0LzZtWDRjUVRldVVleGt1VFZSNHRRcGxiOHpyVGZYR09GeWppb0YvZ2JFRThBSGhyYVdxTkQzY3pXaE8weFFmUkdtZU9rZ3RBYmNwMkhHZlYwVHhEdUdndW1LSmtxTlZQYnFWSjIiLCJtYWMiOiIxMzZiMDhhNTQ3MTk1YWE2NDIzMzRiNzAwMzM0MWFiMzRmNWIzMmE0NGYxMjNlZWVkY2Y1ZDhhNWE5YzQ3YzFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-928379534 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Cu3UyOJpq8Q80cZEpugXvVrolpBjlhoa4viY2usT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928379534\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-880464683 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:52:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBIcVlrcDdnS0ZFdjladGhIRVRHZFE9PSIsInZhbHVlIjoiN3ZHVTZwN2dZOUFLbTdCL3dRbWtieS80NGcweHU1OXd1dkJqcnNUS1ZaNVd1MTRhcnAzekt5RjIxeGpqenRieWlsMUVXQUJ3R3NNQ2crTDAvenpwbytTL3lhTmR1cXZ4dlkwMWlrS2VIemNFUWR3NVJjbmU1eVhvOUNPYW5xNlZIa0VtYTF0cmdKN1VjMmQwRWI2dlFHWGJJTGxHTm12QkVBUHNYTThLNk15dURpalF0TEVmZG01T3N3amU3eDhKUGx3Nkw1MEtoVUhTMGszYm10UitLMGhDa2VPdDIxdEhlWjFUUWtERzlPTTdVOUxlcVVQd3E3YzB4SmswbjNzTWk1WDBpdDJLVDJBbjNzQzc2Wm9nQVN1Wi9HZHQ0TzFYUXRrRk5vKzgzNGNyVE94cUNHR0dIS015V3N4QjlkcEhCd2tjVGpIdkNyT0orRHR3RktUY2N1S1Y0VkVKZWxhWFJUU0kxdGdGTjRiTHRsWDhFL0lMOXVGNG90Z0tSZ1crMEFPS1FxVExDQ0tpNTBHekRodFVKRitGZ09oNTdxVU1LUDBTcU92dXZuNER0N29pNWFGY0JveWdBQk4vd1RKMXREdmxWMzAzcTRtQnFFNCtOUmxxV2JrK1RRNTRNeXVCRHcyUXhzUEdVYk16OEUzbEFHZlJ1RVZCMit5OGxHU0MiLCJtYWMiOiI4Y2Y3YTc4NDIxNWExOTgxZDlmM2NmMTkzMTg3YjlhZWZmMmJiM2EyNGJkMWViYzE1ZTkwY2Q0OTZhMjM3ZWNlIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:52:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjIzWWwxZTZaUDhJQklwd0ZVN0NISnc9PSIsInZhbHVlIjoiL3gza0FweTJIektDR1B2Qlk5UlIwK0FrNEUxSFFDNEt4am1Fb2tXV3NPZTZPcllkZkdkdUtyMWhXQ2VBc3kvLzRqaHFwM2I3b3g4Qi83YldCRnpJMDdCR3d0T1BsMUd4VHRzbVcrWVRtNlpRMGJ2WjE1N25lQlRGaFFaZTIza3pGK0RYeWJrK2F0dmxLVHFRanE0SDExOXZoTjlWTGhnMll5N3VlT3k0NXdRcEhMVEpJeWdXaytudjBHYk8zT2tzNFEweW9JeDNtTGtaaTEzeEo4bHcxeWtYNHRVQUlidk5UUFRuV1Rab2JFRGhBUDBrNGxoMkJtVEpndHEwTExhMDMvYlRqMEdOTm5DRmtaZmNJd2ZtZmd3eWNJbmhJS0VrOWN1SmFzZXd5ei9mWTN4Qmlwa1YwU2RoSkQzRURZWnlDT0ZkbjZsOHRNWGJnS1JXSjdwc2JLSjEzdmpjdEJ5U1A0Rk41ci9jL2hkQTkxdmo0bDIyZlE5UVlXcjJpbXpLV3BWSCtlUnVKQS9pL01nSDZRdHA4b3BDaU9jamE5c3k0Z1dKcWpnaWw1S09NeVV3aC9MYjI0c0M4ZXhvUndZVGozUzFVK2c0NldvZ0Z5QUZGV1VTd0VFcmN2TEY1Z1ErSzU3Rnp4TWd5ZzA0MFFsVTAweFIvMTRVeHZzbUV2a2wiLCJtYWMiOiI0Y2ZiMzJjNWM1MThmM2E3NTQ2ZDNiODAwMGRmNmQxNzFkODY1Njc4MTQ5NzRlZmE2MzEyNGY0MzdlNjFiNzNlIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:52:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBIcVlrcDdnS0ZFdjladGhIRVRHZFE9PSIsInZhbHVlIjoiN3ZHVTZwN2dZOUFLbTdCL3dRbWtieS80NGcweHU1OXd1dkJqcnNUS1ZaNVd1MTRhcnAzekt5RjIxeGpqenRieWlsMUVXQUJ3R3NNQ2crTDAvenpwbytTL3lhTmR1cXZ4dlkwMWlrS2VIemNFUWR3NVJjbmU1eVhvOUNPYW5xNlZIa0VtYTF0cmdKN1VjMmQwRWI2dlFHWGJJTGxHTm12QkVBUHNYTThLNk15dURpalF0TEVmZG01T3N3amU3eDhKUGx3Nkw1MEtoVUhTMGszYm10UitLMGhDa2VPdDIxdEhlWjFUUWtERzlPTTdVOUxlcVVQd3E3YzB4SmswbjNzTWk1WDBpdDJLVDJBbjNzQzc2Wm9nQVN1Wi9HZHQ0TzFYUXRrRk5vKzgzNGNyVE94cUNHR0dIS015V3N4QjlkcEhCd2tjVGpIdkNyT0orRHR3RktUY2N1S1Y0VkVKZWxhWFJUU0kxdGdGTjRiTHRsWDhFL0lMOXVGNG90Z0tSZ1crMEFPS1FxVExDQ0tpNTBHekRodFVKRitGZ09oNTdxVU1LUDBTcU92dXZuNER0N29pNWFGY0JveWdBQk4vd1RKMXREdmxWMzAzcTRtQnFFNCtOUmxxV2JrK1RRNTRNeXVCRHcyUXhzUEdVYk16OEUzbEFHZlJ1RVZCMit5OGxHU0MiLCJtYWMiOiI4Y2Y3YTc4NDIxNWExOTgxZDlmM2NmMTkzMTg3YjlhZWZmMmJiM2EyNGJkMWViYzE1ZTkwY2Q0OTZhMjM3ZWNlIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:52:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjIzWWwxZTZaUDhJQklwd0ZVN0NISnc9PSIsInZhbHVlIjoiL3gza0FweTJIektDR1B2Qlk5UlIwK0FrNEUxSFFDNEt4am1Fb2tXV3NPZTZPcllkZkdkdUtyMWhXQ2VBc3kvLzRqaHFwM2I3b3g4Qi83YldCRnpJMDdCR3d0T1BsMUd4VHRzbVcrWVRtNlpRMGJ2WjE1N25lQlRGaFFaZTIza3pGK0RYeWJrK2F0dmxLVHFRanE0SDExOXZoTjlWTGhnMll5N3VlT3k0NXdRcEhMVEpJeWdXaytudjBHYk8zT2tzNFEweW9JeDNtTGtaaTEzeEo4bHcxeWtYNHRVQUlidk5UUFRuV1Rab2JFRGhBUDBrNGxoMkJtVEpndHEwTExhMDMvYlRqMEdOTm5DRmtaZmNJd2ZtZmd3eWNJbmhJS0VrOWN1SmFzZXd5ei9mWTN4Qmlwa1YwU2RoSkQzRURZWnlDT0ZkbjZsOHRNWGJnS1JXSjdwc2JLSjEzdmpjdEJ5U1A0Rk41ci9jL2hkQTkxdmo0bDIyZlE5UVlXcjJpbXpLV3BWSCtlUnVKQS9pL01nSDZRdHA4b3BDaU9jamE5c3k0Z1dKcWpnaWw1S09NeVV3aC9MYjI0c0M4ZXhvUndZVGozUzFVK2c0NldvZ0Z5QUZGV1VTd0VFcmN2TEY1Z1ErSzU3Rnp4TWd5ZzA0MFFsVTAweFIvMTRVeHZzbUV2a2wiLCJtYWMiOiI0Y2ZiMzJjNWM1MThmM2E3NTQ2ZDNiODAwMGRmNmQxNzFkODY1Njc4MTQ5NzRlZmE2MzEyNGY0MzdlNjFiNzNlIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:52:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-880464683\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-154062383 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1TEWgDeuhxpQmxjqljvtNZoOZR3gm6gIVXQikBTx</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154062383\", {\"maxDepth\":0})</script>\n"}}