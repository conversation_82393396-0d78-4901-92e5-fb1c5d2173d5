{"__meta": {"id": "Xb64d5ef5f120b73f4e8e4750c2064196", "datetime": "2025-07-21 00:51:20", "utime": 1753059080.152353, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753059062.982214, "end": 1753059080.152376, "duration": 17.170161962509155, "duration_str": "17.17s", "measures": [{"label": "Booting", "start": 1753059062.982214, "relative_start": 0, "end": 1753059075.656611, "relative_end": 1753059075.656611, "duration": 12.674396991729736, "duration_str": "12.67s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753059075.656646, "relative_start": 12.674432039260864, "end": 1753059080.152378, "relative_end": 2.1457672119140625e-06, "duration": 4.495732069015503, "duration_str": "4.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44311928, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "271ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.25937, "duration": 0.*****************, "duration_str": "271ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "epTOk1SGzRnwC8ywLaFyBduumxNQQAhwuF3K3k4Y", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1527133005 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1527133005\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1526615699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1526615699\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-737718566 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"195 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737718566\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1124425264 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124425264\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-454251026 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:51:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllGK3cycXBvSFpLc2FMNzFnR1lmNXc9PSIsInZhbHVlIjoiemZvNThEbitJNmVtMTNTelI1MDFXRmFheGxBaFJXbU4waTFVbDFFZTFkb3FOV3NSQmp5MG5RUnh4RURHa2Zhd1ROUm5ITHJpdE9iWHBwM2JiNlFzQkcrRWFQNm0ySmczU09uMjFySWIrUzdGNExiYkM5SGNua3dLRG5RT21hcTNqVWRhdk1tT1ZPbkZJbnAwelZYNXM3enVQMmJyTnB3RkxBL3hZbU1vaUQ0bTN3ZmZ3aS94eTNPMFJURkxOUld2S0d6cjZha1pZeEhnSVUxSGNjNXVEZzEzZkU5VVBuYXZWNGNtT2JHV3NranhrbmxBaytNTUFLUDAwMlErdW1hc3picWM3WkduKzBIZGpBMFUyL3o0RjJqMUtIdjZia2N1TVdGSzVCaW5CKzFjazM1R1oxR0ZZWW1HVWtONThlbEpnRUROT0JtaUlGMVFYSEVLdUZsWTNJUGNQajBkSnMzQWFXTERVVmZ3T0c5ZFoxN3BUdEpUdGpqS1FEc1hybUN6QXdwKzZaTXdFVlVKZzJpZlRyS3gvTk9hdkhyMTUzYWIvOFFJYjdoZ2RqYjN0YktnTG1wRnU2R3VscUM0TlIzcFNYdTV2TDVIc1VZVUdyNlFRK3JrZTkyUklSZ0JGVnFIaHpIK2dQUm9zSkRCQ1dKYkxnZmpoWUFUelRveTZvc3giLCJtYWMiOiI0MjAzN2U0NDU2NTg5OWYxNDJkZTFmY2VjYzUwZTIxMmE1ZGM0YmYwNmUyM2RhM2E3N2ViNGZlOWVhYjdjNWY0IiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:51:19 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRIMk9RV25UVlNZd0JHbDdBeWtIclE9PSIsInZhbHVlIjoiY1RzZGwxZDJ0MGpSanNIQTRwZ3dYKzBLQmlwcDVXK0tDZFVJNy9VMkJsL0syVTVTQ05Xa0xzTXQ0VzczTEVRT0RzN1dpTjZOQTlPUnJQaHAvckxmcU0xOC9teXgvb0hROUpIck9CY0xJS2VPdHk1RkJMNWx3c3Nhc0JTSFQ0dEh1T2RvbncyRmxvTWUweDU1dzE0ZkNXeXpsYjR0dG5DWWs2Z0M5VDVDUkFMZmJwSEtBTmx0UU51OW9vcnRvbFdjYTN2VnNmSGVHakh3WEYzcEJaaVlhUmliTzBtZmd2ZHR6Wm0xcitrMHVhODVWMFVHTHFBMFhzc2ZzUlhyRFV4UVQrOE9qWXBJRjF5MW91WmIzU0JBRDZwVjE1cFlXNmVFTk92bG1iSHFZY0VRcENEREZVN3FIMTI4Rk9EQkQ2VHhmLzFQK1owWGowNjJsSjVTK0tTdlpPd3J5TVpRTVJCOE1FMXFJYStZOWQwT1VhZnNNQ0xoNSsrTmhmZ0hza2V1T1BLeFE3ZDRjTDhYNzVjTEpKUjNaVTRCaXVaOHhhdmEwYWVud1NFTnkwNE5yajd1dGFrVXNYcnRXZGxQdFRDWVpBdDRqNzNlQk1sOSs0UVRrc3Z3WUV5MWE4UkJESDVvL0JNWWNsYjN6Z09CTnNuUVpySmRiMTQyL1Z0M3dWUXkiLCJtYWMiOiIyNjVmOGJkYjQxZDI5MTRlYzliOWFjMTU4MzNkMThjZGQ1YmM3Mjc3ZGQ5MWI1ZTdjY2ZiOTU0MWM4OGY3ZmMyIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:51:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllGK3cycXBvSFpLc2FMNzFnR1lmNXc9PSIsInZhbHVlIjoiemZvNThEbitJNmVtMTNTelI1MDFXRmFheGxBaFJXbU4waTFVbDFFZTFkb3FOV3NSQmp5MG5RUnh4RURHa2Zhd1ROUm5ITHJpdE9iWHBwM2JiNlFzQkcrRWFQNm0ySmczU09uMjFySWIrUzdGNExiYkM5SGNua3dLRG5RT21hcTNqVWRhdk1tT1ZPbkZJbnAwelZYNXM3enVQMmJyTnB3RkxBL3hZbU1vaUQ0bTN3ZmZ3aS94eTNPMFJURkxOUld2S0d6cjZha1pZeEhnSVUxSGNjNXVEZzEzZkU5VVBuYXZWNGNtT2JHV3NranhrbmxBaytNTUFLUDAwMlErdW1hc3picWM3WkduKzBIZGpBMFUyL3o0RjJqMUtIdjZia2N1TVdGSzVCaW5CKzFjazM1R1oxR0ZZWW1HVWtONThlbEpnRUROT0JtaUlGMVFYSEVLdUZsWTNJUGNQajBkSnMzQWFXTERVVmZ3T0c5ZFoxN3BUdEpUdGpqS1FEc1hybUN6QXdwKzZaTXdFVlVKZzJpZlRyS3gvTk9hdkhyMTUzYWIvOFFJYjdoZ2RqYjN0YktnTG1wRnU2R3VscUM0TlIzcFNYdTV2TDVIc1VZVUdyNlFRK3JrZTkyUklSZ0JGVnFIaHpIK2dQUm9zSkRCQ1dKYkxnZmpoWUFUelRveTZvc3giLCJtYWMiOiI0MjAzN2U0NDU2NTg5OWYxNDJkZTFmY2VjYzUwZTIxMmE1ZGM0YmYwNmUyM2RhM2E3N2ViNGZlOWVhYjdjNWY0IiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:51:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRIMk9RV25UVlNZd0JHbDdBeWtIclE9PSIsInZhbHVlIjoiY1RzZGwxZDJ0MGpSanNIQTRwZ3dYKzBLQmlwcDVXK0tDZFVJNy9VMkJsL0syVTVTQ05Xa0xzTXQ0VzczTEVRT0RzN1dpTjZOQTlPUnJQaHAvckxmcU0xOC9teXgvb0hROUpIck9CY0xJS2VPdHk1RkJMNWx3c3Nhc0JTSFQ0dEh1T2RvbncyRmxvTWUweDU1dzE0ZkNXeXpsYjR0dG5DWWs2Z0M5VDVDUkFMZmJwSEtBTmx0UU51OW9vcnRvbFdjYTN2VnNmSGVHakh3WEYzcEJaaVlhUmliTzBtZmd2ZHR6Wm0xcitrMHVhODVWMFVHTHFBMFhzc2ZzUlhyRFV4UVQrOE9qWXBJRjF5MW91WmIzU0JBRDZwVjE1cFlXNmVFTk92bG1iSHFZY0VRcENEREZVN3FIMTI4Rk9EQkQ2VHhmLzFQK1owWGowNjJsSjVTK0tTdlpPd3J5TVpRTVJCOE1FMXFJYStZOWQwT1VhZnNNQ0xoNSsrTmhmZ0hza2V1T1BLeFE3ZDRjTDhYNzVjTEpKUjNaVTRCaXVaOHhhdmEwYWVud1NFTnkwNE5yajd1dGFrVXNYcnRXZGxQdFRDWVpBdDRqNzNlQk1sOSs0UVRrc3Z3WUV5MWE4UkJESDVvL0JNWWNsYjN6Z09CTnNuUVpySmRiMTQyL1Z0M3dWUXkiLCJtYWMiOiIyNjVmOGJkYjQxZDI5MTRlYzliOWFjMTU4MzNkMThjZGQ1YmM3Mjc3ZGQ5MWI1ZTdjY2ZiOTU0MWM4OGY3ZmMyIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:51:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-454251026\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1662116858 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">epTOk1SGzRnwC8ywLaFyBduumxNQQAhwuF3K3k4Y</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662116858\", {\"maxDepth\":0})</script>\n"}}