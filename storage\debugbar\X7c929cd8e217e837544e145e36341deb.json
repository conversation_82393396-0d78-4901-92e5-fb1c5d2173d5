{"__meta": {"id": "X7c929cd8e217e837544e145e36341deb", "datetime": "2025-07-21 00:56:27", "utime": **********.738919, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.133803, "end": **********.738932, "duration": 0.6051290035247803, "duration_str": "605ms", "measures": [{"label": "Booting", "start": **********.133803, "relative_start": 0, "end": **********.57612, "relative_end": **********.57612, "duration": 0.44231700897216797, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.576128, "relative_start": 0.4423251152038574, "end": **********.738933, "relative_end": 1.1920928955078125e-06, "duration": 0.16280508041381836, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46309768, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02611, "accumulated_duration_str": "26.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.618098, "duration": 0.02494, "duration_str": "24.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.519}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.653239, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.519, "width_percent": 1.915}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6561391, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.434, "width_percent": 2.566}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1121181707 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1121181707\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1896924558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1896924558\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2075728555 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075728555\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1212131049 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxs%7C0%7C2019; _clsk=16aumtd%7C1753059218308%7C1%7C1%7Cd.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdvdUpERWszT2tqZGRDekR0eDNLOEE9PSIsInZhbHVlIjoiN2R4SzAyUTRUUjRLSWRIU2djU2FNcCtvTkxjbmJtQW9oTS9FeGdZckMybFlHeWRwRFhDQld0WXJHY0Vyam5jYmpBbmhlcDdjdWU4VGRNdWF4TnVaMkJoWmhsU2ZiQUNxY0Mvc2dQeDFDREpvZFpBakhLVkF4ZW5VZGpUNUgwbGd0Ynh6VklRK3g2WmxnYWlFeGFVOFF3NWpyNWRKQnRoTVJsZUlyTTVNbjBNcExaemdoSVpDR0JhczFqNG9LQWY0dUpET3N0WXorRmJYMXlubEVBbWtwWkhTWm9SQTNBN3BvaG1xOEpaTmRBMHBhNnNYZ2xyWGlNcWhKWGhWOTNEVnpSNFFjYXErQUlvTDJPbzZ6cnJyaG5UYVY0dm56MTdkWEw1QkNjekYxU0thOVlFbytKS3hxY09XdnoyS2t3QjlqNzBCWUxmR2lFa1ZvdUpXdERKMEJJMGhuZUNuSDVXUEdxcjZoMWg3TWJUanhOQTdiZkZHY2ZQUFprOUdEdGc4NWFXcnVVZmZvUkFmY25qcytWSTJaSWl0YnhCRVg5ZHdLU1BqQTRQT0p1NGZDZm9wajFLTU56NFhBOTlQMk1EUWlJNHFFMlNTME90MThLdWFXcGt4R1BaZzVLdWxyd05oN2RSQXRaTlczaU5qUFllNlc3ZWt2NUZNL2hsWlZxY1giLCJtYWMiOiI1MWVkZDBiZDBmOGZkNWEyOTNlNDUwYzQyNGI0MmNmNWViYjI1NzUzMmIxMTBhZGRjOTBjMjdmOGNkZTRlNDUxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZsbHB0ZEpUclJCTXpFcFhOUk54MkE9PSIsInZhbHVlIjoiTzloMDd1TVp3aUczWDhhd25aNTgwUmxpV2FyYnR1c0pKRUJ1ckNHTjI0NHBmRjVudkVKcTFZZnN0T2xjdFFLL3A1eXB2T0lvSlJMMlpLOHBNTUd2aGphZkk1ZXJiZkdXNHNKOTlZaTVhay9kS1FjbUg5a1ZxYVRqSnlNbUhhU0VuWmdRTGozY0hYYnZIQWQzTjI1RGRpR0N1TnBMbGVVRzY1T0pXNnNLNk14aE9Xams2T3hNK1BhSVNDSUVncUYyU3Q2MFk4SWxjeDNRRUphbmlSdjNCemJURUhHL2FJYkVFZFNVWVdxb0NZTnA5NVIrKzMyS1ZvSFdFdmdhMDJrTVRBeTZzZklvQ3dNNHhvRWovdnJwYVFIMzh3cFYrN0NYUkVweVpDQUg5ckNjSHVUaTA5N3RrMTFTbyt3NXlhVFdlMUJQbmhNVkQ4SWRQOVhxTWpJQmNRSFZuS1RUQjJvMDZoRC92bzVsS1FBd3EvWEdSb3cwRzJTTzlBam1ISGJwcE9reTI0TmZaQW1MUjU4bmZ3WCtyUzJKZkdSSFQ0TzVFZXB3andENng5L1plRE8zRUlnM3gyUE1FZ0Y1ZjFIcFpmbjVDeGJkUGcrRldKVHhxU0hIOEY0b0tyWmI0VnFtQWRXTEtQV0diOUo0b3dKMFlSM0pDQ090dVZLdkdNenoiLCJtYWMiOiJmOWE3M2MzYzkxNzQ3MDNkMzRkYWE1MjQ3MGZmZDgwMTRmNzBmYTBkMjMzY2FjODY4NGUwOTYyMmI2ZjQyM2VmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212131049\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-277326767 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWZVjOmV9W6QT2qBzflrYxNxEoMyYDFB4iGBh4Fb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277326767\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2033360659 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 00:56:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhFL0FjZWQ2UDJuTHpmVElxQ08vNlE9PSIsInZhbHVlIjoiMDVrcFdka05FSVJBd0xrRWU4azI5bEZGQ3AwclVlS05Iam8rakZxZzBBeVRlbUZZMHlwdjJsTHRuY2JkT1BLQ3VqVUN4OG5Jbzk5T0ZLYkFMUUs5THpZVTB6bHVEWU9makVvS2kxcGNLa2hnYkcxb0dHbW9aNmJaWEI2S2x5dGNzbCtyV2FlZHN0SUlyOHh6dlhIeG03UEo0MDM0TXNld0V2LzhLSFZidkk5d25Xa3lJVzZsU0V1NkFjUzZrNWpaZlJsQUpBd1ZERk1nZU11S0Z0NXRtc3BoVmFGWmhXWU5qK0ZudXoxd2VieGhOZDU1VkpocVMybkpCc1h1cWx6NEdzK2VRK1VJSkhMemFzb0w5a3ZsMGk4ZnhDVy80RC83SHU0WjJIdG80RkdyQUV2U3dzZzY0RHlSTDJaZVFhMnpJZXVyU0JEOEdaNkZxUkZDb2NueVFIWjJrWG1RVW9QREkzNnlra240cU5vUzFLRE01VlZBTXNvVXpTY1hrQnV3MzN0cDVnVTZqVUxoM1NnT1QxWWd3VmpMWW9UUWxWa2tVZWwvUUl4SkV4L1ZsL1dpcjlyRDZWRzBoRnI5WkMvcUx6VStwV2tJbUFmN3V5TDA2S1U2R2ZFWGZpalpFaTg0ZHIyanIvSXFlSHI2NVBPNVA5MzJybGxwaElLaUdydXciLCJtYWMiOiI4NDE0YTI2NDQ1MDcyOWFiZGRmZWJjNWU1M2YxMWMzMzNjYmIzYWJhYjMzNGI0M2EyOWNhOWVjODA3YWM3NWFlIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJJc0RvWnVCZ1RBNzduU0w3T0pwTGc9PSIsInZhbHVlIjoicVdCNTNYakZXaVlhcCszMkpKeG41SVRzV2RvZlVTK1h5NnIwUUg4MlNES3UveVlOV0N4UkNpMXBYbCtzaUREclo5TlhXdzE3TVh1WFRWZC9Ob0hGSDdOOFpKRmczdmR3T3BJQ3o5KzRDdi9aUzBIRjNSeG9xUWJ6amVnYkVUU2ZvZENwczg1TERHY3cyVmk5MjlVeStJOWVLc2RtZzk0bVZGRERERFBuUmV2ZWhoVk5NWisxZS9QdTgxSXNneFA4MXZsUkxxMDZZa0xQZkdyZkxxNTlCUW43bzE4MmJFOHdaVVIyek1FTEpranpaOGI3YWRlQ2dEbzA5ZGhGY2ZGOGRqKzhBalRlOFhuMVYyRUxLeGpDdm4yd2FWT2xqTnZTZGNJb1k4Z1BsRE5DN0ZZREpDWTNRaTlNRkgzWkJZUEx2YXB5U1NSU1FDaHZTT00yMDV0Q3RJblUvV3hmMG1ib0NlRnJGWHg5WWEzQWdDNC9pTnloK1JvZnVCcUI3bjNLVUhqNU11aUlocGx5VmptTktYamUzUmJrVFpqU3BxcnZyenE5emZ5cEJ5NkFEcXRrSmlSTEpjcEdEZjZFdWVpV3BVZENDN0hjK0RZZjBhbWdGanpUUkpOaWM3RUQ3YVJlS0hXTGpiZW84T1VTaWJvVW56eERLMWlldU10NXZNTWgiLCJtYWMiOiIzNThhMDczMzQ3ZWI5MWM5N2Y0ZjFiOTQwMjk5MGE4NTgxYjFmMjljMTIyMTFjOTcyNTRmN2Q0MDhkOTM0NWZkIiwidGFnIjoiIn0%3D; expires=Mon, 21 Jul 2025 02:56:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhFL0FjZWQ2UDJuTHpmVElxQ08vNlE9PSIsInZhbHVlIjoiMDVrcFdka05FSVJBd0xrRWU4azI5bEZGQ3AwclVlS05Iam8rakZxZzBBeVRlbUZZMHlwdjJsTHRuY2JkT1BLQ3VqVUN4OG5Jbzk5T0ZLYkFMUUs5THpZVTB6bHVEWU9makVvS2kxcGNLa2hnYkcxb0dHbW9aNmJaWEI2S2x5dGNzbCtyV2FlZHN0SUlyOHh6dlhIeG03UEo0MDM0TXNld0V2LzhLSFZidkk5d25Xa3lJVzZsU0V1NkFjUzZrNWpaZlJsQUpBd1ZERk1nZU11S0Z0NXRtc3BoVmFGWmhXWU5qK0ZudXoxd2VieGhOZDU1VkpocVMybkpCc1h1cWx6NEdzK2VRK1VJSkhMemFzb0w5a3ZsMGk4ZnhDVy80RC83SHU0WjJIdG80RkdyQUV2U3dzZzY0RHlSTDJaZVFhMnpJZXVyU0JEOEdaNkZxUkZDb2NueVFIWjJrWG1RVW9QREkzNnlra240cU5vUzFLRE01VlZBTXNvVXpTY1hrQnV3MzN0cDVnVTZqVUxoM1NnT1QxWWd3VmpMWW9UUWxWa2tVZWwvUUl4SkV4L1ZsL1dpcjlyRDZWRzBoRnI5WkMvcUx6VStwV2tJbUFmN3V5TDA2S1U2R2ZFWGZpalpFaTg0ZHIyanIvSXFlSHI2NVBPNVA5MzJybGxwaElLaUdydXciLCJtYWMiOiI4NDE0YTI2NDQ1MDcyOWFiZGRmZWJjNWU1M2YxMWMzMzNjYmIzYWJhYjMzNGI0M2EyOWNhOWVjODA3YWM3NWFlIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJJc0RvWnVCZ1RBNzduU0w3T0pwTGc9PSIsInZhbHVlIjoicVdCNTNYakZXaVlhcCszMkpKeG41SVRzV2RvZlVTK1h5NnIwUUg4MlNES3UveVlOV0N4UkNpMXBYbCtzaUREclo5TlhXdzE3TVh1WFRWZC9Ob0hGSDdOOFpKRmczdmR3T3BJQ3o5KzRDdi9aUzBIRjNSeG9xUWJ6amVnYkVUU2ZvZENwczg1TERHY3cyVmk5MjlVeStJOWVLc2RtZzk0bVZGRERERFBuUmV2ZWhoVk5NWisxZS9QdTgxSXNneFA4MXZsUkxxMDZZa0xQZkdyZkxxNTlCUW43bzE4MmJFOHdaVVIyek1FTEpranpaOGI3YWRlQ2dEbzA5ZGhGY2ZGOGRqKzhBalRlOFhuMVYyRUxLeGpDdm4yd2FWT2xqTnZTZGNJb1k4Z1BsRE5DN0ZZREpDWTNRaTlNRkgzWkJZUEx2YXB5U1NSU1FDaHZTT00yMDV0Q3RJblUvV3hmMG1ib0NlRnJGWHg5WWEzQWdDNC9pTnloK1JvZnVCcUI3bjNLVUhqNU11aUlocGx5VmptTktYamUzUmJrVFpqU3BxcnZyenE5emZ5cEJ5NkFEcXRrSmlSTEpjcEdEZjZFdWVpV3BVZENDN0hjK0RZZjBhbWdGanpUUkpOaWM3RUQ3YVJlS0hXTGpiZW84T1VTaWJvVW56eERLMWlldU10NXZNTWgiLCJtYWMiOiIzNThhMDczMzQ3ZWI5MWM5N2Y0ZjFiOTQwMjk5MGE4NTgxYjFmMjljMTIyMTFjOTcyNTRmN2Q0MDhkOTM0NWZkIiwidGFnIjoiIn0%3D; expires=Mon, 21-Jul-2025 02:56:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033360659\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-900468627 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hjW71ULginx21xRixTtHMdSZXUdAkKshiXrUYTHy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900468627\", {\"maxDepth\":0})</script>\n"}}